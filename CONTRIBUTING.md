# How to Contribute

`Silver Surfer` is [Apache 2.0 licensed](LICENSE) and accepts contributions via GitHub
pull requests. This document outlines some coding conventions, contact points and other resources to make
it easier to get your contribution accepted.

We gratefully welcome improvements to issues and documentation as well as to code.

## Certificate of Origin

By contributing to this project you agree to the Developer Certificate of
Origin (DCO). This document was created by the Linux Kernel community and is a
simple statement that you, as a contributor, have the legal right to make the
contribution. No action from you is required, but it's a good idea to see the
[DCO](DCO) file for details before you start contributing code to Devtron.

## Submitting changes

Please send a [GitHub Pull Request to silver-surfer](https://github.com/devtron-labs/silver-surfer/pull/new/main) with a clear list of what you've done (read more about [pull requests](https://docs.github.com/en/github/collaborating-with-pull-requests/proposing-changes-to-your-work-with-pull-requests/about-pull-requests)).
We can always use more test coverage. Please follow our coding conventions (below).

Always write a clear messages for your commits. 


## Coding conventions

Start reading our code and you'll get the hang of it. We optimize for readability:

* Please always format the code before you create a pull request. Know more about [go fmt tool](https://go.dev/blog/gofmt)
* Please include test cases covering all the base & corner cases.
* This is open source software. Consider the people who will read your code, and make it look nice for them. It's sort of like driving a car: Perhaps you love drifting when you're alone, but with passengers the goal is to make the ride as smooth as possible.
* Please update the documentation in case of new feature or enhancement.

## Communications

The project uses discord for communication:

To join the conversation, simply join the **[discord](https://discord.gg/jsRG5qx2gp)**  and use the __#contrib__ channel.