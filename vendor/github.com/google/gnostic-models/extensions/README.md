# Extensions

**Extension Support is experimental.**

This directory contains support code for building Gnostic extensio handlers and
associated examples.

Extension handlers can be used to compile vendor or specification extensions
into protocol buffer structures.

Like plugins, extension handlers are built as separate executables. Extension
bodies are written to extension handlers as serialized
ExtensionHandlerRequests.
