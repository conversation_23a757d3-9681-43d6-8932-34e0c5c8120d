# This is the official list of people who can contribute
# (and typically have contributed) code to the Wire repository.
# The AUTHORS file lists the copyright holders; this file
# lists people.  For example, Google employees are listed here
# but not in AUTHORS, because Google holds the copyright.
#
# Names should be added to this file only after verifying that
# the individual or the individual's organization has agreed to
# the appropriate Contributor License Agreement, found here:
#
#     http://code.google.com/legal/individual-cla-v1.0.html
#     http://code.google.com/legal/corporate-cla-v1.0.html
#
# The agreement for individuals can be filled out on the web.
#
# When adding J Random Contributor's name to this file,
# either <PERSON>'s name or <PERSON>'s organization's name should be
# added to the AUTHORS file, depending on whether the
# individual or corporate CLA was used.

# Names should be added to this file like so:
#     Individual's name <submission email address>
#     Individual's name <submission email address> <email2> <emailN>
#
# An entry with multiple email addresses specifies that the
# first address should be used in the submit logs and
# that the other addresses should be recognized as the
# same person when interacting with <PERSON><PERSON>.

# Please keep the list sorted.

<PERSON> <<EMAIL>> <<EMAIL>> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON><PERSON> <<EMAIL>>
Issac Trotts <<EMAIL>> <<EMAIL>>
ktr <<EMAIL>>
Kumbirai Tanekha <<EMAIL>>
Oleg Kovalov <<EMAIL>>
Robert van Gent <<EMAIL>> <<EMAIL>>
Ross Light <<EMAIL>> <<EMAIL>>
Tuo Shan <<EMAIL>> <<EMAIL>>
Yoichiro Shimizu <<EMAIL>>
Zachary Romero <<EMAIL>>
