// Copyright © 2018 <PERSON> <<EMAIL>>.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package afero

import (
	"os"
)

// Lstater is an optional interface in Afero. It is only implemented by the
// filesystems saying so.
// It will call <PERSON>sta<PERSON> if the filesystem iself is, or it delegates to, the os filesystem.
// Else it will call Stat.
// In addtion to the FileInfo, it will return a boolean telling whether <PERSON><PERSON><PERSON> was called or not.
type Lstater interface {
	LstatIfPossible(name string) (os.FileInfo, bool, error)
}
