/*
 *
 * Copyright 2024 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package grpc

// ServerStreamingClient represents the client side of a server-streaming (one
// request, many responses) RPC. It is generic over the type of the response
// message. It is used in generated code.
type ServerStreamingClient[Res any] interface {
	// Recv receives the next response message from the server. The client may
	// repeatedly call Recv to read messages from the response stream.  If
	// io.EOF is returned, the stream has terminated with an OK status.  Any
	// other error is compatible with the status package and indicates the
	// RPC's status code and message.
	Recv() (*Res, error)

	// ClientStream is embedded to provide Context, Header, and Trailer
	// functionality.  No other methods in the ClientStream should be called
	// directly.
	ClientStream
}

// ServerStreamingServer represents the server side of a server-streaming (one
// request, many responses) RPC. It is generic over the type of the response
// message. It is used in generated code.
//
// To terminate the response stream, return from the handler method and return
// an error from the status package, or use nil to indicate an OK status code.
type ServerStreamingServer[Res any] interface {
	// Send sends a response message to the client.  The server handler may
	// call Send multiple times to send multiple messages to the client.  An
	// error is returned if the stream was terminated unexpectedly, and the
	// handler method should return, as the stream is no longer usable.
	Send(*Res) error

	// ServerStream is embedded to provide Context, SetHeader, SendHeader, and
	// SetTrailer functionality.  No other methods in the ServerStream should
	// be called directly.
	ServerStream
}

// ClientStreamingClient represents the client side of a client-streaming (many
// requests, one response) RPC. It is generic over both the type of the request
// message stream and the type of the unary response message. It is used in
// generated code.
type ClientStreamingClient[Req any, Res any] interface {
	// Send sends a request message to the server.  The client may call Send
	// multiple times to send multiple messages to the server.  On error, Send
	// aborts the stream.  If the error was generated by the client, the status
	// is returned directly.  Otherwise, io.EOF is returned, and the status of
	// the stream may be discovered using CloseAndRecv().
	Send(*Req) error

	// CloseAndRecv closes the request stream and waits for the server's
	// response.  This method must be called once and only once after sending
	// all request messages.  Any error returned is implemented by the status
	// package.
	CloseAndRecv() (*Res, error)

	// ClientStream is embedded to provide Context, Header, and Trailer
	// functionality.  No other methods in the ClientStream should be called
	// directly.
	ClientStream
}

// ClientStreamingServer represents the server side of a client-streaming (many
// requests, one response) RPC. It is generic over both the type of the request
// message stream and the type of the unary response message. It is used in
// generated code.
//
// To terminate the RPC, call SendAndClose and return nil from the method
// handler or do not call SendAndClose and return an error from the status
// package.
type ClientStreamingServer[Req any, Res any] interface {
	// Recv receives the next request message from the client.  The server may
	// repeatedly call Recv to read messages from the request stream.  If
	// io.EOF is returned, it indicates the client called CloseAndRecv on its
	// ClientStreamingClient.  Any other error indicates the stream was
	// terminated unexpectedly, and the handler method should return, as the
	// stream is no longer usable.
	Recv() (*Req, error)

	// SendAndClose sends a single response message to the client and closes
	// the stream.  This method must be called once and only once after all
	// request messages have been processed.  Recv should not be called after
	// calling SendAndClose.
	SendAndClose(*Res) error

	// ServerStream is embedded to provide Context, SetHeader, SendHeader, and
	// SetTrailer functionality.  No other methods in the ServerStream should
	// be called directly.
	ServerStream
}

// BidiStreamingClient represents the client side of a bidirectional-streaming
// (many requests, many responses) RPC. It is generic over both the type of the
// request message stream and the type of the response message stream. It is
// used in generated code.
type BidiStreamingClient[Req any, Res any] interface {
	// Send sends a request message to the server.  The client may call Send
	// multiple times to send multiple messages to the server.  On error, Send
	// aborts the stream.  If the error was generated by the client, the status
	// is returned directly.  Otherwise, io.EOF is returned, and the status of
	// the stream may be discovered using Recv().
	Send(*Req) error

	// Recv receives the next response message from the server. The client may
	// repeatedly call Recv to read messages from the response stream.  If
	// io.EOF is returned, the stream has terminated with an OK status.  Any
	// other error is compatible with the status package and indicates the
	// RPC's status code and message.
	Recv() (*Res, error)

	// ClientStream is embedded to provide Context, Header, Trailer, and
	// CloseSend functionality.  No other methods in the ClientStream should be
	// called directly.
	ClientStream
}

// BidiStreamingServer represents the server side of a bidirectional-streaming
// (many requests, many responses) RPC. It is generic over both the type of the
// request message stream and the type of the response message stream. It is
// used in generated code.
//
// To terminate the stream, return from the handler method and return
// an error from the status package, or use nil to indicate an OK status code.
type BidiStreamingServer[Req any, Res any] interface {
	// Recv receives the next request message from the client.  The server may
	// repeatedly call Recv to read messages from the request stream.  If
	// io.EOF is returned, it indicates the client called CloseSend on its
	// BidiStreamingClient.  Any other error indicates the stream was
	// terminated unexpectedly, and the handler method should return, as the
	// stream is no longer usable.
	Recv() (*Req, error)

	// Send sends a response message to the client.  The server handler may
	// call Send multiple times to send multiple messages to the client.  An
	// error is returned if the stream was terminated unexpectedly, and the
	// handler method should return, as the stream is no longer usable.
	Send(*Res) error

	// ServerStream is embedded to provide Context, SetHeader, SendHeader, and
	// SetTrailer functionality.  No other methods in the ServerStream should
	// be called directly.
	ServerStream
}

// GenericClientStream implements the ServerStreamingClient, ClientStreamingClient,
// and BidiStreamingClient interfaces. It is used in generated code.
type GenericClientStream[Req any, Res any] struct {
	ClientStream
}

var _ ServerStreamingClient[string] = (*GenericClientStream[int, string])(nil)
var _ ClientStreamingClient[int, string] = (*GenericClientStream[int, string])(nil)
var _ BidiStreamingClient[int, string] = (*GenericClientStream[int, string])(nil)

// Send pushes one message into the stream of requests to be consumed by the
// server. The type of message which can be sent is determined by the Req type
// parameter of the GenericClientStream receiver.
func (x *GenericClientStream[Req, Res]) Send(m *Req) error {
	return x.ClientStream.SendMsg(m)
}

// Recv reads one message from the stream of responses generated by the server.
// The type of the message returned is determined by the Res type parameter
// of the GenericClientStream receiver.
func (x *GenericClientStream[Req, Res]) Recv() (*Res, error) {
	m := new(Res)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// CloseAndRecv closes the sending side of the stream, then receives the unary
// response from the server. The type of message which it returns is determined
// by the Res type parameter of the GenericClientStream receiver.
func (x *GenericClientStream[Req, Res]) CloseAndRecv() (*Res, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(Res)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// GenericServerStream implements the ServerStreamingServer, ClientStreamingServer,
// and BidiStreamingServer interfaces. It is used in generated code.
type GenericServerStream[Req any, Res any] struct {
	ServerStream
}

var _ ServerStreamingServer[string] = (*GenericServerStream[int, string])(nil)
var _ ClientStreamingServer[int, string] = (*GenericServerStream[int, string])(nil)
var _ BidiStreamingServer[int, string] = (*GenericServerStream[int, string])(nil)

// Send pushes one message into the stream of responses to be consumed by the
// client. The type of message which can be sent is determined by the Res
// type parameter of the serverStreamServer receiver.
func (x *GenericServerStream[Req, Res]) Send(m *Res) error {
	return x.ServerStream.SendMsg(m)
}

// SendAndClose pushes the unary response to the client. The type of message
// which can be sent is determined by the Res type parameter of the
// clientStreamServer receiver.
func (x *GenericServerStream[Req, Res]) SendAndClose(m *Res) error {
	return x.ServerStream.SendMsg(m)
}

// Recv reads one message from the stream of requests generated by the client.
// The type of the message returned is determined by the Req type parameter
// of the clientStreamServer receiver.
func (x *GenericServerStream[Req, Res]) Recv() (*Req, error) {
	m := new(Req)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}
