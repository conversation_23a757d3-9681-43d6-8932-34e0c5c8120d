// Copyright 2019 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by generate-protos. DO NOT EDIT.

package genid

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
)

const File_google_protobuf_descriptor_proto = "google/protobuf/descriptor.proto"

// Full and short names for google.protobuf.Edition.
const (
	Edition_enum_fullname = "google.protobuf.Edition"
	Edition_enum_name     = "Edition"
)

// Enum values for google.protobuf.Edition.
const (
	Edition_EDITION_UNKNOWN_enum_value         = 0
	Edition_EDITION_LEGACY_enum_value          = 900
	Edition_EDITION_PROTO2_enum_value          = 998
	Edition_EDITION_PROTO3_enum_value          = 999
	Edition_EDITION_2023_enum_value            = 1000
	Edition_EDITION_2024_enum_value            = 1001
	Edition_EDITION_1_TEST_ONLY_enum_value     = 1
	Edition_EDITION_2_TEST_ONLY_enum_value     = 2
	Edition_EDITION_99997_TEST_ONLY_enum_value = 99997
	Edition_EDITION_99998_TEST_ONLY_enum_value = 99998
	Edition_EDITION_99999_TEST_ONLY_enum_value = 99999
	Edition_EDITION_MAX_enum_value             = 2147483647
)

// Names for google.protobuf.FileDescriptorSet.
const (
	FileDescriptorSet_message_name     protoreflect.Name     = "FileDescriptorSet"
	FileDescriptorSet_message_fullname protoreflect.FullName = "google.protobuf.FileDescriptorSet"
)

// Field names for google.protobuf.FileDescriptorSet.
const (
	FileDescriptorSet_File_field_name protoreflect.Name = "file"

	FileDescriptorSet_File_field_fullname protoreflect.FullName = "google.protobuf.FileDescriptorSet.file"
)

// Field numbers for google.protobuf.FileDescriptorSet.
const (
	FileDescriptorSet_File_field_number protoreflect.FieldNumber = 1
)

// Names for google.protobuf.FileDescriptorProto.
const (
	FileDescriptorProto_message_name     protoreflect.Name     = "FileDescriptorProto"
	FileDescriptorProto_message_fullname protoreflect.FullName = "google.protobuf.FileDescriptorProto"
)

// Field names for google.protobuf.FileDescriptorProto.
const (
	FileDescriptorProto_Name_field_name             protoreflect.Name = "name"
	FileDescriptorProto_Package_field_name          protoreflect.Name = "package"
	FileDescriptorProto_Dependency_field_name       protoreflect.Name = "dependency"
	FileDescriptorProto_PublicDependency_field_name protoreflect.Name = "public_dependency"
	FileDescriptorProto_WeakDependency_field_name   protoreflect.Name = "weak_dependency"
	FileDescriptorProto_MessageType_field_name      protoreflect.Name = "message_type"
	FileDescriptorProto_EnumType_field_name         protoreflect.Name = "enum_type"
	FileDescriptorProto_Service_field_name          protoreflect.Name = "service"
	FileDescriptorProto_Extension_field_name        protoreflect.Name = "extension"
	FileDescriptorProto_Options_field_name          protoreflect.Name = "options"
	FileDescriptorProto_SourceCodeInfo_field_name   protoreflect.Name = "source_code_info"
	FileDescriptorProto_Syntax_field_name           protoreflect.Name = "syntax"
	FileDescriptorProto_Edition_field_name          protoreflect.Name = "edition"

	FileDescriptorProto_Name_field_fullname             protoreflect.FullName = "google.protobuf.FileDescriptorProto.name"
	FileDescriptorProto_Package_field_fullname          protoreflect.FullName = "google.protobuf.FileDescriptorProto.package"
	FileDescriptorProto_Dependency_field_fullname       protoreflect.FullName = "google.protobuf.FileDescriptorProto.dependency"
	FileDescriptorProto_PublicDependency_field_fullname protoreflect.FullName = "google.protobuf.FileDescriptorProto.public_dependency"
	FileDescriptorProto_WeakDependency_field_fullname   protoreflect.FullName = "google.protobuf.FileDescriptorProto.weak_dependency"
	FileDescriptorProto_MessageType_field_fullname      protoreflect.FullName = "google.protobuf.FileDescriptorProto.message_type"
	FileDescriptorProto_EnumType_field_fullname         protoreflect.FullName = "google.protobuf.FileDescriptorProto.enum_type"
	FileDescriptorProto_Service_field_fullname          protoreflect.FullName = "google.protobuf.FileDescriptorProto.service"
	FileDescriptorProto_Extension_field_fullname        protoreflect.FullName = "google.protobuf.FileDescriptorProto.extension"
	FileDescriptorProto_Options_field_fullname          protoreflect.FullName = "google.protobuf.FileDescriptorProto.options"
	FileDescriptorProto_SourceCodeInfo_field_fullname   protoreflect.FullName = "google.protobuf.FileDescriptorProto.source_code_info"
	FileDescriptorProto_Syntax_field_fullname           protoreflect.FullName = "google.protobuf.FileDescriptorProto.syntax"
	FileDescriptorProto_Edition_field_fullname          protoreflect.FullName = "google.protobuf.FileDescriptorProto.edition"
)

// Field numbers for google.protobuf.FileDescriptorProto.
const (
	FileDescriptorProto_Name_field_number             protoreflect.FieldNumber = 1
	FileDescriptorProto_Package_field_number          protoreflect.FieldNumber = 2
	FileDescriptorProto_Dependency_field_number       protoreflect.FieldNumber = 3
	FileDescriptorProto_PublicDependency_field_number protoreflect.FieldNumber = 10
	FileDescriptorProto_WeakDependency_field_number   protoreflect.FieldNumber = 11
	FileDescriptorProto_MessageType_field_number      protoreflect.FieldNumber = 4
	FileDescriptorProto_EnumType_field_number         protoreflect.FieldNumber = 5
	FileDescriptorProto_Service_field_number          protoreflect.FieldNumber = 6
	FileDescriptorProto_Extension_field_number        protoreflect.FieldNumber = 7
	FileDescriptorProto_Options_field_number          protoreflect.FieldNumber = 8
	FileDescriptorProto_SourceCodeInfo_field_number   protoreflect.FieldNumber = 9
	FileDescriptorProto_Syntax_field_number           protoreflect.FieldNumber = 12
	FileDescriptorProto_Edition_field_number          protoreflect.FieldNumber = 14
)

// Names for google.protobuf.DescriptorProto.
const (
	DescriptorProto_message_name     protoreflect.Name     = "DescriptorProto"
	DescriptorProto_message_fullname protoreflect.FullName = "google.protobuf.DescriptorProto"
)

// Field names for google.protobuf.DescriptorProto.
const (
	DescriptorProto_Name_field_name           protoreflect.Name = "name"
	DescriptorProto_Field_field_name          protoreflect.Name = "field"
	DescriptorProto_Extension_field_name      protoreflect.Name = "extension"
	DescriptorProto_NestedType_field_name     protoreflect.Name = "nested_type"
	DescriptorProto_EnumType_field_name       protoreflect.Name = "enum_type"
	DescriptorProto_ExtensionRange_field_name protoreflect.Name = "extension_range"
	DescriptorProto_OneofDecl_field_name      protoreflect.Name = "oneof_decl"
	DescriptorProto_Options_field_name        protoreflect.Name = "options"
	DescriptorProto_ReservedRange_field_name  protoreflect.Name = "reserved_range"
	DescriptorProto_ReservedName_field_name   protoreflect.Name = "reserved_name"

	DescriptorProto_Name_field_fullname           protoreflect.FullName = "google.protobuf.DescriptorProto.name"
	DescriptorProto_Field_field_fullname          protoreflect.FullName = "google.protobuf.DescriptorProto.field"
	DescriptorProto_Extension_field_fullname      protoreflect.FullName = "google.protobuf.DescriptorProto.extension"
	DescriptorProto_NestedType_field_fullname     protoreflect.FullName = "google.protobuf.DescriptorProto.nested_type"
	DescriptorProto_EnumType_field_fullname       protoreflect.FullName = "google.protobuf.DescriptorProto.enum_type"
	DescriptorProto_ExtensionRange_field_fullname protoreflect.FullName = "google.protobuf.DescriptorProto.extension_range"
	DescriptorProto_OneofDecl_field_fullname      protoreflect.FullName = "google.protobuf.DescriptorProto.oneof_decl"
	DescriptorProto_Options_field_fullname        protoreflect.FullName = "google.protobuf.DescriptorProto.options"
	DescriptorProto_ReservedRange_field_fullname  protoreflect.FullName = "google.protobuf.DescriptorProto.reserved_range"
	DescriptorProto_ReservedName_field_fullname   protoreflect.FullName = "google.protobuf.DescriptorProto.reserved_name"
)

// Field numbers for google.protobuf.DescriptorProto.
const (
	DescriptorProto_Name_field_number           protoreflect.FieldNumber = 1
	DescriptorProto_Field_field_number          protoreflect.FieldNumber = 2
	DescriptorProto_Extension_field_number      protoreflect.FieldNumber = 6
	DescriptorProto_NestedType_field_number     protoreflect.FieldNumber = 3
	DescriptorProto_EnumType_field_number       protoreflect.FieldNumber = 4
	DescriptorProto_ExtensionRange_field_number protoreflect.FieldNumber = 5
	DescriptorProto_OneofDecl_field_number      protoreflect.FieldNumber = 8
	DescriptorProto_Options_field_number        protoreflect.FieldNumber = 7
	DescriptorProto_ReservedRange_field_number  protoreflect.FieldNumber = 9
	DescriptorProto_ReservedName_field_number   protoreflect.FieldNumber = 10
)

// Names for google.protobuf.DescriptorProto.ExtensionRange.
const (
	DescriptorProto_ExtensionRange_message_name     protoreflect.Name     = "ExtensionRange"
	DescriptorProto_ExtensionRange_message_fullname protoreflect.FullName = "google.protobuf.DescriptorProto.ExtensionRange"
)

// Field names for google.protobuf.DescriptorProto.ExtensionRange.
const (
	DescriptorProto_ExtensionRange_Start_field_name   protoreflect.Name = "start"
	DescriptorProto_ExtensionRange_End_field_name     protoreflect.Name = "end"
	DescriptorProto_ExtensionRange_Options_field_name protoreflect.Name = "options"

	DescriptorProto_ExtensionRange_Start_field_fullname   protoreflect.FullName = "google.protobuf.DescriptorProto.ExtensionRange.start"
	DescriptorProto_ExtensionRange_End_field_fullname     protoreflect.FullName = "google.protobuf.DescriptorProto.ExtensionRange.end"
	DescriptorProto_ExtensionRange_Options_field_fullname protoreflect.FullName = "google.protobuf.DescriptorProto.ExtensionRange.options"
)

// Field numbers for google.protobuf.DescriptorProto.ExtensionRange.
const (
	DescriptorProto_ExtensionRange_Start_field_number   protoreflect.FieldNumber = 1
	DescriptorProto_ExtensionRange_End_field_number     protoreflect.FieldNumber = 2
	DescriptorProto_ExtensionRange_Options_field_number protoreflect.FieldNumber = 3
)

// Names for google.protobuf.DescriptorProto.ReservedRange.
const (
	DescriptorProto_ReservedRange_message_name     protoreflect.Name     = "ReservedRange"
	DescriptorProto_ReservedRange_message_fullname protoreflect.FullName = "google.protobuf.DescriptorProto.ReservedRange"
)

// Field names for google.protobuf.DescriptorProto.ReservedRange.
const (
	DescriptorProto_ReservedRange_Start_field_name protoreflect.Name = "start"
	DescriptorProto_ReservedRange_End_field_name   protoreflect.Name = "end"

	DescriptorProto_ReservedRange_Start_field_fullname protoreflect.FullName = "google.protobuf.DescriptorProto.ReservedRange.start"
	DescriptorProto_ReservedRange_End_field_fullname   protoreflect.FullName = "google.protobuf.DescriptorProto.ReservedRange.end"
)

// Field numbers for google.protobuf.DescriptorProto.ReservedRange.
const (
	DescriptorProto_ReservedRange_Start_field_number protoreflect.FieldNumber = 1
	DescriptorProto_ReservedRange_End_field_number   protoreflect.FieldNumber = 2
)

// Names for google.protobuf.ExtensionRangeOptions.
const (
	ExtensionRangeOptions_message_name     protoreflect.Name     = "ExtensionRangeOptions"
	ExtensionRangeOptions_message_fullname protoreflect.FullName = "google.protobuf.ExtensionRangeOptions"
)

// Field names for google.protobuf.ExtensionRangeOptions.
const (
	ExtensionRangeOptions_UninterpretedOption_field_name protoreflect.Name = "uninterpreted_option"
	ExtensionRangeOptions_Declaration_field_name         protoreflect.Name = "declaration"
	ExtensionRangeOptions_Features_field_name            protoreflect.Name = "features"
	ExtensionRangeOptions_Verification_field_name        protoreflect.Name = "verification"

	ExtensionRangeOptions_UninterpretedOption_field_fullname protoreflect.FullName = "google.protobuf.ExtensionRangeOptions.uninterpreted_option"
	ExtensionRangeOptions_Declaration_field_fullname         protoreflect.FullName = "google.protobuf.ExtensionRangeOptions.declaration"
	ExtensionRangeOptions_Features_field_fullname            protoreflect.FullName = "google.protobuf.ExtensionRangeOptions.features"
	ExtensionRangeOptions_Verification_field_fullname        protoreflect.FullName = "google.protobuf.ExtensionRangeOptions.verification"
)

// Field numbers for google.protobuf.ExtensionRangeOptions.
const (
	ExtensionRangeOptions_UninterpretedOption_field_number protoreflect.FieldNumber = 999
	ExtensionRangeOptions_Declaration_field_number         protoreflect.FieldNumber = 2
	ExtensionRangeOptions_Features_field_number            protoreflect.FieldNumber = 50
	ExtensionRangeOptions_Verification_field_number        protoreflect.FieldNumber = 3
)

// Full and short names for google.protobuf.ExtensionRangeOptions.VerificationState.
const (
	ExtensionRangeOptions_VerificationState_enum_fullname = "google.protobuf.ExtensionRangeOptions.VerificationState"
	ExtensionRangeOptions_VerificationState_enum_name     = "VerificationState"
)

// Enum values for google.protobuf.ExtensionRangeOptions.VerificationState.
const (
	ExtensionRangeOptions_DECLARATION_enum_value = 0
	ExtensionRangeOptions_UNVERIFIED_enum_value  = 1
)

// Names for google.protobuf.ExtensionRangeOptions.Declaration.
const (
	ExtensionRangeOptions_Declaration_message_name     protoreflect.Name     = "Declaration"
	ExtensionRangeOptions_Declaration_message_fullname protoreflect.FullName = "google.protobuf.ExtensionRangeOptions.Declaration"
)

// Field names for google.protobuf.ExtensionRangeOptions.Declaration.
const (
	ExtensionRangeOptions_Declaration_Number_field_name   protoreflect.Name = "number"
	ExtensionRangeOptions_Declaration_FullName_field_name protoreflect.Name = "full_name"
	ExtensionRangeOptions_Declaration_Type_field_name     protoreflect.Name = "type"
	ExtensionRangeOptions_Declaration_Reserved_field_name protoreflect.Name = "reserved"
	ExtensionRangeOptions_Declaration_Repeated_field_name protoreflect.Name = "repeated"

	ExtensionRangeOptions_Declaration_Number_field_fullname   protoreflect.FullName = "google.protobuf.ExtensionRangeOptions.Declaration.number"
	ExtensionRangeOptions_Declaration_FullName_field_fullname protoreflect.FullName = "google.protobuf.ExtensionRangeOptions.Declaration.full_name"
	ExtensionRangeOptions_Declaration_Type_field_fullname     protoreflect.FullName = "google.protobuf.ExtensionRangeOptions.Declaration.type"
	ExtensionRangeOptions_Declaration_Reserved_field_fullname protoreflect.FullName = "google.protobuf.ExtensionRangeOptions.Declaration.reserved"
	ExtensionRangeOptions_Declaration_Repeated_field_fullname protoreflect.FullName = "google.protobuf.ExtensionRangeOptions.Declaration.repeated"
)

// Field numbers for google.protobuf.ExtensionRangeOptions.Declaration.
const (
	ExtensionRangeOptions_Declaration_Number_field_number   protoreflect.FieldNumber = 1
	ExtensionRangeOptions_Declaration_FullName_field_number protoreflect.FieldNumber = 2
	ExtensionRangeOptions_Declaration_Type_field_number     protoreflect.FieldNumber = 3
	ExtensionRangeOptions_Declaration_Reserved_field_number protoreflect.FieldNumber = 5
	ExtensionRangeOptions_Declaration_Repeated_field_number protoreflect.FieldNumber = 6
)

// Names for google.protobuf.FieldDescriptorProto.
const (
	FieldDescriptorProto_message_name     protoreflect.Name     = "FieldDescriptorProto"
	FieldDescriptorProto_message_fullname protoreflect.FullName = "google.protobuf.FieldDescriptorProto"
)

// Field names for google.protobuf.FieldDescriptorProto.
const (
	FieldDescriptorProto_Name_field_name           protoreflect.Name = "name"
	FieldDescriptorProto_Number_field_name         protoreflect.Name = "number"
	FieldDescriptorProto_Label_field_name          protoreflect.Name = "label"
	FieldDescriptorProto_Type_field_name           protoreflect.Name = "type"
	FieldDescriptorProto_TypeName_field_name       protoreflect.Name = "type_name"
	FieldDescriptorProto_Extendee_field_name       protoreflect.Name = "extendee"
	FieldDescriptorProto_DefaultValue_field_name   protoreflect.Name = "default_value"
	FieldDescriptorProto_OneofIndex_field_name     protoreflect.Name = "oneof_index"
	FieldDescriptorProto_JsonName_field_name       protoreflect.Name = "json_name"
	FieldDescriptorProto_Options_field_name        protoreflect.Name = "options"
	FieldDescriptorProto_Proto3Optional_field_name protoreflect.Name = "proto3_optional"

	FieldDescriptorProto_Name_field_fullname           protoreflect.FullName = "google.protobuf.FieldDescriptorProto.name"
	FieldDescriptorProto_Number_field_fullname         protoreflect.FullName = "google.protobuf.FieldDescriptorProto.number"
	FieldDescriptorProto_Label_field_fullname          protoreflect.FullName = "google.protobuf.FieldDescriptorProto.label"
	FieldDescriptorProto_Type_field_fullname           protoreflect.FullName = "google.protobuf.FieldDescriptorProto.type"
	FieldDescriptorProto_TypeName_field_fullname       protoreflect.FullName = "google.protobuf.FieldDescriptorProto.type_name"
	FieldDescriptorProto_Extendee_field_fullname       protoreflect.FullName = "google.protobuf.FieldDescriptorProto.extendee"
	FieldDescriptorProto_DefaultValue_field_fullname   protoreflect.FullName = "google.protobuf.FieldDescriptorProto.default_value"
	FieldDescriptorProto_OneofIndex_field_fullname     protoreflect.FullName = "google.protobuf.FieldDescriptorProto.oneof_index"
	FieldDescriptorProto_JsonName_field_fullname       protoreflect.FullName = "google.protobuf.FieldDescriptorProto.json_name"
	FieldDescriptorProto_Options_field_fullname        protoreflect.FullName = "google.protobuf.FieldDescriptorProto.options"
	FieldDescriptorProto_Proto3Optional_field_fullname protoreflect.FullName = "google.protobuf.FieldDescriptorProto.proto3_optional"
)

// Field numbers for google.protobuf.FieldDescriptorProto.
const (
	FieldDescriptorProto_Name_field_number           protoreflect.FieldNumber = 1
	FieldDescriptorProto_Number_field_number         protoreflect.FieldNumber = 3
	FieldDescriptorProto_Label_field_number          protoreflect.FieldNumber = 4
	FieldDescriptorProto_Type_field_number           protoreflect.FieldNumber = 5
	FieldDescriptorProto_TypeName_field_number       protoreflect.FieldNumber = 6
	FieldDescriptorProto_Extendee_field_number       protoreflect.FieldNumber = 2
	FieldDescriptorProto_DefaultValue_field_number   protoreflect.FieldNumber = 7
	FieldDescriptorProto_OneofIndex_field_number     protoreflect.FieldNumber = 9
	FieldDescriptorProto_JsonName_field_number       protoreflect.FieldNumber = 10
	FieldDescriptorProto_Options_field_number        protoreflect.FieldNumber = 8
	FieldDescriptorProto_Proto3Optional_field_number protoreflect.FieldNumber = 17
)

// Full and short names for google.protobuf.FieldDescriptorProto.Type.
const (
	FieldDescriptorProto_Type_enum_fullname = "google.protobuf.FieldDescriptorProto.Type"
	FieldDescriptorProto_Type_enum_name     = "Type"
)

// Enum values for google.protobuf.FieldDescriptorProto.Type.
const (
	FieldDescriptorProto_TYPE_DOUBLE_enum_value   = 1
	FieldDescriptorProto_TYPE_FLOAT_enum_value    = 2
	FieldDescriptorProto_TYPE_INT64_enum_value    = 3
	FieldDescriptorProto_TYPE_UINT64_enum_value   = 4
	FieldDescriptorProto_TYPE_INT32_enum_value    = 5
	FieldDescriptorProto_TYPE_FIXED64_enum_value  = 6
	FieldDescriptorProto_TYPE_FIXED32_enum_value  = 7
	FieldDescriptorProto_TYPE_BOOL_enum_value     = 8
	FieldDescriptorProto_TYPE_STRING_enum_value   = 9
	FieldDescriptorProto_TYPE_GROUP_enum_value    = 10
	FieldDescriptorProto_TYPE_MESSAGE_enum_value  = 11
	FieldDescriptorProto_TYPE_BYTES_enum_value    = 12
	FieldDescriptorProto_TYPE_UINT32_enum_value   = 13
	FieldDescriptorProto_TYPE_ENUM_enum_value     = 14
	FieldDescriptorProto_TYPE_SFIXED32_enum_value = 15
	FieldDescriptorProto_TYPE_SFIXED64_enum_value = 16
	FieldDescriptorProto_TYPE_SINT32_enum_value   = 17
	FieldDescriptorProto_TYPE_SINT64_enum_value   = 18
)

// Full and short names for google.protobuf.FieldDescriptorProto.Label.
const (
	FieldDescriptorProto_Label_enum_fullname = "google.protobuf.FieldDescriptorProto.Label"
	FieldDescriptorProto_Label_enum_name     = "Label"
)

// Enum values for google.protobuf.FieldDescriptorProto.Label.
const (
	FieldDescriptorProto_LABEL_OPTIONAL_enum_value = 1
	FieldDescriptorProto_LABEL_REPEATED_enum_value = 3
	FieldDescriptorProto_LABEL_REQUIRED_enum_value = 2
)

// Names for google.protobuf.OneofDescriptorProto.
const (
	OneofDescriptorProto_message_name     protoreflect.Name     = "OneofDescriptorProto"
	OneofDescriptorProto_message_fullname protoreflect.FullName = "google.protobuf.OneofDescriptorProto"
)

// Field names for google.protobuf.OneofDescriptorProto.
const (
	OneofDescriptorProto_Name_field_name    protoreflect.Name = "name"
	OneofDescriptorProto_Options_field_name protoreflect.Name = "options"

	OneofDescriptorProto_Name_field_fullname    protoreflect.FullName = "google.protobuf.OneofDescriptorProto.name"
	OneofDescriptorProto_Options_field_fullname protoreflect.FullName = "google.protobuf.OneofDescriptorProto.options"
)

// Field numbers for google.protobuf.OneofDescriptorProto.
const (
	OneofDescriptorProto_Name_field_number    protoreflect.FieldNumber = 1
	OneofDescriptorProto_Options_field_number protoreflect.FieldNumber = 2
)

// Names for google.protobuf.EnumDescriptorProto.
const (
	EnumDescriptorProto_message_name     protoreflect.Name     = "EnumDescriptorProto"
	EnumDescriptorProto_message_fullname protoreflect.FullName = "google.protobuf.EnumDescriptorProto"
)

// Field names for google.protobuf.EnumDescriptorProto.
const (
	EnumDescriptorProto_Name_field_name          protoreflect.Name = "name"
	EnumDescriptorProto_Value_field_name         protoreflect.Name = "value"
	EnumDescriptorProto_Options_field_name       protoreflect.Name = "options"
	EnumDescriptorProto_ReservedRange_field_name protoreflect.Name = "reserved_range"
	EnumDescriptorProto_ReservedName_field_name  protoreflect.Name = "reserved_name"

	EnumDescriptorProto_Name_field_fullname          protoreflect.FullName = "google.protobuf.EnumDescriptorProto.name"
	EnumDescriptorProto_Value_field_fullname         protoreflect.FullName = "google.protobuf.EnumDescriptorProto.value"
	EnumDescriptorProto_Options_field_fullname       protoreflect.FullName = "google.protobuf.EnumDescriptorProto.options"
	EnumDescriptorProto_ReservedRange_field_fullname protoreflect.FullName = "google.protobuf.EnumDescriptorProto.reserved_range"
	EnumDescriptorProto_ReservedName_field_fullname  protoreflect.FullName = "google.protobuf.EnumDescriptorProto.reserved_name"
)

// Field numbers for google.protobuf.EnumDescriptorProto.
const (
	EnumDescriptorProto_Name_field_number          protoreflect.FieldNumber = 1
	EnumDescriptorProto_Value_field_number         protoreflect.FieldNumber = 2
	EnumDescriptorProto_Options_field_number       protoreflect.FieldNumber = 3
	EnumDescriptorProto_ReservedRange_field_number protoreflect.FieldNumber = 4
	EnumDescriptorProto_ReservedName_field_number  protoreflect.FieldNumber = 5
)

// Names for google.protobuf.EnumDescriptorProto.EnumReservedRange.
const (
	EnumDescriptorProto_EnumReservedRange_message_name     protoreflect.Name     = "EnumReservedRange"
	EnumDescriptorProto_EnumReservedRange_message_fullname protoreflect.FullName = "google.protobuf.EnumDescriptorProto.EnumReservedRange"
)

// Field names for google.protobuf.EnumDescriptorProto.EnumReservedRange.
const (
	EnumDescriptorProto_EnumReservedRange_Start_field_name protoreflect.Name = "start"
	EnumDescriptorProto_EnumReservedRange_End_field_name   protoreflect.Name = "end"

	EnumDescriptorProto_EnumReservedRange_Start_field_fullname protoreflect.FullName = "google.protobuf.EnumDescriptorProto.EnumReservedRange.start"
	EnumDescriptorProto_EnumReservedRange_End_field_fullname   protoreflect.FullName = "google.protobuf.EnumDescriptorProto.EnumReservedRange.end"
)

// Field numbers for google.protobuf.EnumDescriptorProto.EnumReservedRange.
const (
	EnumDescriptorProto_EnumReservedRange_Start_field_number protoreflect.FieldNumber = 1
	EnumDescriptorProto_EnumReservedRange_End_field_number   protoreflect.FieldNumber = 2
)

// Names for google.protobuf.EnumValueDescriptorProto.
const (
	EnumValueDescriptorProto_message_name     protoreflect.Name     = "EnumValueDescriptorProto"
	EnumValueDescriptorProto_message_fullname protoreflect.FullName = "google.protobuf.EnumValueDescriptorProto"
)

// Field names for google.protobuf.EnumValueDescriptorProto.
const (
	EnumValueDescriptorProto_Name_field_name    protoreflect.Name = "name"
	EnumValueDescriptorProto_Number_field_name  protoreflect.Name = "number"
	EnumValueDescriptorProto_Options_field_name protoreflect.Name = "options"

	EnumValueDescriptorProto_Name_field_fullname    protoreflect.FullName = "google.protobuf.EnumValueDescriptorProto.name"
	EnumValueDescriptorProto_Number_field_fullname  protoreflect.FullName = "google.protobuf.EnumValueDescriptorProto.number"
	EnumValueDescriptorProto_Options_field_fullname protoreflect.FullName = "google.protobuf.EnumValueDescriptorProto.options"
)

// Field numbers for google.protobuf.EnumValueDescriptorProto.
const (
	EnumValueDescriptorProto_Name_field_number    protoreflect.FieldNumber = 1
	EnumValueDescriptorProto_Number_field_number  protoreflect.FieldNumber = 2
	EnumValueDescriptorProto_Options_field_number protoreflect.FieldNumber = 3
)

// Names for google.protobuf.ServiceDescriptorProto.
const (
	ServiceDescriptorProto_message_name     protoreflect.Name     = "ServiceDescriptorProto"
	ServiceDescriptorProto_message_fullname protoreflect.FullName = "google.protobuf.ServiceDescriptorProto"
)

// Field names for google.protobuf.ServiceDescriptorProto.
const (
	ServiceDescriptorProto_Name_field_name    protoreflect.Name = "name"
	ServiceDescriptorProto_Method_field_name  protoreflect.Name = "method"
	ServiceDescriptorProto_Options_field_name protoreflect.Name = "options"

	ServiceDescriptorProto_Name_field_fullname    protoreflect.FullName = "google.protobuf.ServiceDescriptorProto.name"
	ServiceDescriptorProto_Method_field_fullname  protoreflect.FullName = "google.protobuf.ServiceDescriptorProto.method"
	ServiceDescriptorProto_Options_field_fullname protoreflect.FullName = "google.protobuf.ServiceDescriptorProto.options"
)

// Field numbers for google.protobuf.ServiceDescriptorProto.
const (
	ServiceDescriptorProto_Name_field_number    protoreflect.FieldNumber = 1
	ServiceDescriptorProto_Method_field_number  protoreflect.FieldNumber = 2
	ServiceDescriptorProto_Options_field_number protoreflect.FieldNumber = 3
)

// Names for google.protobuf.MethodDescriptorProto.
const (
	MethodDescriptorProto_message_name     protoreflect.Name     = "MethodDescriptorProto"
	MethodDescriptorProto_message_fullname protoreflect.FullName = "google.protobuf.MethodDescriptorProto"
)

// Field names for google.protobuf.MethodDescriptorProto.
const (
	MethodDescriptorProto_Name_field_name            protoreflect.Name = "name"
	MethodDescriptorProto_InputType_field_name       protoreflect.Name = "input_type"
	MethodDescriptorProto_OutputType_field_name      protoreflect.Name = "output_type"
	MethodDescriptorProto_Options_field_name         protoreflect.Name = "options"
	MethodDescriptorProto_ClientStreaming_field_name protoreflect.Name = "client_streaming"
	MethodDescriptorProto_ServerStreaming_field_name protoreflect.Name = "server_streaming"

	MethodDescriptorProto_Name_field_fullname            protoreflect.FullName = "google.protobuf.MethodDescriptorProto.name"
	MethodDescriptorProto_InputType_field_fullname       protoreflect.FullName = "google.protobuf.MethodDescriptorProto.input_type"
	MethodDescriptorProto_OutputType_field_fullname      protoreflect.FullName = "google.protobuf.MethodDescriptorProto.output_type"
	MethodDescriptorProto_Options_field_fullname         protoreflect.FullName = "google.protobuf.MethodDescriptorProto.options"
	MethodDescriptorProto_ClientStreaming_field_fullname protoreflect.FullName = "google.protobuf.MethodDescriptorProto.client_streaming"
	MethodDescriptorProto_ServerStreaming_field_fullname protoreflect.FullName = "google.protobuf.MethodDescriptorProto.server_streaming"
)

// Field numbers for google.protobuf.MethodDescriptorProto.
const (
	MethodDescriptorProto_Name_field_number            protoreflect.FieldNumber = 1
	MethodDescriptorProto_InputType_field_number       protoreflect.FieldNumber = 2
	MethodDescriptorProto_OutputType_field_number      protoreflect.FieldNumber = 3
	MethodDescriptorProto_Options_field_number         protoreflect.FieldNumber = 4
	MethodDescriptorProto_ClientStreaming_field_number protoreflect.FieldNumber = 5
	MethodDescriptorProto_ServerStreaming_field_number protoreflect.FieldNumber = 6
)

// Names for google.protobuf.FileOptions.
const (
	FileOptions_message_name     protoreflect.Name     = "FileOptions"
	FileOptions_message_fullname protoreflect.FullName = "google.protobuf.FileOptions"
)

// Field names for google.protobuf.FileOptions.
const (
	FileOptions_JavaPackage_field_name               protoreflect.Name = "java_package"
	FileOptions_JavaOuterClassname_field_name        protoreflect.Name = "java_outer_classname"
	FileOptions_JavaMultipleFiles_field_name         protoreflect.Name = "java_multiple_files"
	FileOptions_JavaGenerateEqualsAndHash_field_name protoreflect.Name = "java_generate_equals_and_hash"
	FileOptions_JavaStringCheckUtf8_field_name       protoreflect.Name = "java_string_check_utf8"
	FileOptions_OptimizeFor_field_name               protoreflect.Name = "optimize_for"
	FileOptions_GoPackage_field_name                 protoreflect.Name = "go_package"
	FileOptions_CcGenericServices_field_name         protoreflect.Name = "cc_generic_services"
	FileOptions_JavaGenericServices_field_name       protoreflect.Name = "java_generic_services"
	FileOptions_PyGenericServices_field_name         protoreflect.Name = "py_generic_services"
	FileOptions_Deprecated_field_name                protoreflect.Name = "deprecated"
	FileOptions_CcEnableArenas_field_name            protoreflect.Name = "cc_enable_arenas"
	FileOptions_ObjcClassPrefix_field_name           protoreflect.Name = "objc_class_prefix"
	FileOptions_CsharpNamespace_field_name           protoreflect.Name = "csharp_namespace"
	FileOptions_SwiftPrefix_field_name               protoreflect.Name = "swift_prefix"
	FileOptions_PhpClassPrefix_field_name            protoreflect.Name = "php_class_prefix"
	FileOptions_PhpNamespace_field_name              protoreflect.Name = "php_namespace"
	FileOptions_PhpMetadataNamespace_field_name      protoreflect.Name = "php_metadata_namespace"
	FileOptions_RubyPackage_field_name               protoreflect.Name = "ruby_package"
	FileOptions_Features_field_name                  protoreflect.Name = "features"
	FileOptions_UninterpretedOption_field_name       protoreflect.Name = "uninterpreted_option"

	FileOptions_JavaPackage_field_fullname               protoreflect.FullName = "google.protobuf.FileOptions.java_package"
	FileOptions_JavaOuterClassname_field_fullname        protoreflect.FullName = "google.protobuf.FileOptions.java_outer_classname"
	FileOptions_JavaMultipleFiles_field_fullname         protoreflect.FullName = "google.protobuf.FileOptions.java_multiple_files"
	FileOptions_JavaGenerateEqualsAndHash_field_fullname protoreflect.FullName = "google.protobuf.FileOptions.java_generate_equals_and_hash"
	FileOptions_JavaStringCheckUtf8_field_fullname       protoreflect.FullName = "google.protobuf.FileOptions.java_string_check_utf8"
	FileOptions_OptimizeFor_field_fullname               protoreflect.FullName = "google.protobuf.FileOptions.optimize_for"
	FileOptions_GoPackage_field_fullname                 protoreflect.FullName = "google.protobuf.FileOptions.go_package"
	FileOptions_CcGenericServices_field_fullname         protoreflect.FullName = "google.protobuf.FileOptions.cc_generic_services"
	FileOptions_JavaGenericServices_field_fullname       protoreflect.FullName = "google.protobuf.FileOptions.java_generic_services"
	FileOptions_PyGenericServices_field_fullname         protoreflect.FullName = "google.protobuf.FileOptions.py_generic_services"
	FileOptions_Deprecated_field_fullname                protoreflect.FullName = "google.protobuf.FileOptions.deprecated"
	FileOptions_CcEnableArenas_field_fullname            protoreflect.FullName = "google.protobuf.FileOptions.cc_enable_arenas"
	FileOptions_ObjcClassPrefix_field_fullname           protoreflect.FullName = "google.protobuf.FileOptions.objc_class_prefix"
	FileOptions_CsharpNamespace_field_fullname           protoreflect.FullName = "google.protobuf.FileOptions.csharp_namespace"
	FileOptions_SwiftPrefix_field_fullname               protoreflect.FullName = "google.protobuf.FileOptions.swift_prefix"
	FileOptions_PhpClassPrefix_field_fullname            protoreflect.FullName = "google.protobuf.FileOptions.php_class_prefix"
	FileOptions_PhpNamespace_field_fullname              protoreflect.FullName = "google.protobuf.FileOptions.php_namespace"
	FileOptions_PhpMetadataNamespace_field_fullname      protoreflect.FullName = "google.protobuf.FileOptions.php_metadata_namespace"
	FileOptions_RubyPackage_field_fullname               protoreflect.FullName = "google.protobuf.FileOptions.ruby_package"
	FileOptions_Features_field_fullname                  protoreflect.FullName = "google.protobuf.FileOptions.features"
	FileOptions_UninterpretedOption_field_fullname       protoreflect.FullName = "google.protobuf.FileOptions.uninterpreted_option"
)

// Field numbers for google.protobuf.FileOptions.
const (
	FileOptions_JavaPackage_field_number               protoreflect.FieldNumber = 1
	FileOptions_JavaOuterClassname_field_number        protoreflect.FieldNumber = 8
	FileOptions_JavaMultipleFiles_field_number         protoreflect.FieldNumber = 10
	FileOptions_JavaGenerateEqualsAndHash_field_number protoreflect.FieldNumber = 20
	FileOptions_JavaStringCheckUtf8_field_number       protoreflect.FieldNumber = 27
	FileOptions_OptimizeFor_field_number               protoreflect.FieldNumber = 9
	FileOptions_GoPackage_field_number                 protoreflect.FieldNumber = 11
	FileOptions_CcGenericServices_field_number         protoreflect.FieldNumber = 16
	FileOptions_JavaGenericServices_field_number       protoreflect.FieldNumber = 17
	FileOptions_PyGenericServices_field_number         protoreflect.FieldNumber = 18
	FileOptions_Deprecated_field_number                protoreflect.FieldNumber = 23
	FileOptions_CcEnableArenas_field_number            protoreflect.FieldNumber = 31
	FileOptions_ObjcClassPrefix_field_number           protoreflect.FieldNumber = 36
	FileOptions_CsharpNamespace_field_number           protoreflect.FieldNumber = 37
	FileOptions_SwiftPrefix_field_number               protoreflect.FieldNumber = 39
	FileOptions_PhpClassPrefix_field_number            protoreflect.FieldNumber = 40
	FileOptions_PhpNamespace_field_number              protoreflect.FieldNumber = 41
	FileOptions_PhpMetadataNamespace_field_number      protoreflect.FieldNumber = 44
	FileOptions_RubyPackage_field_number               protoreflect.FieldNumber = 45
	FileOptions_Features_field_number                  protoreflect.FieldNumber = 50
	FileOptions_UninterpretedOption_field_number       protoreflect.FieldNumber = 999
)

// Full and short names for google.protobuf.FileOptions.OptimizeMode.
const (
	FileOptions_OptimizeMode_enum_fullname = "google.protobuf.FileOptions.OptimizeMode"
	FileOptions_OptimizeMode_enum_name     = "OptimizeMode"
)

// Enum values for google.protobuf.FileOptions.OptimizeMode.
const (
	FileOptions_SPEED_enum_value        = 1
	FileOptions_CODE_SIZE_enum_value    = 2
	FileOptions_LITE_RUNTIME_enum_value = 3
)

// Names for google.protobuf.MessageOptions.
const (
	MessageOptions_message_name     protoreflect.Name     = "MessageOptions"
	MessageOptions_message_fullname protoreflect.FullName = "google.protobuf.MessageOptions"
)

// Field names for google.protobuf.MessageOptions.
const (
	MessageOptions_MessageSetWireFormat_field_name               protoreflect.Name = "message_set_wire_format"
	MessageOptions_NoStandardDescriptorAccessor_field_name       protoreflect.Name = "no_standard_descriptor_accessor"
	MessageOptions_Deprecated_field_name                         protoreflect.Name = "deprecated"
	MessageOptions_MapEntry_field_name                           protoreflect.Name = "map_entry"
	MessageOptions_DeprecatedLegacyJsonFieldConflicts_field_name protoreflect.Name = "deprecated_legacy_json_field_conflicts"
	MessageOptions_Features_field_name                           protoreflect.Name = "features"
	MessageOptions_UninterpretedOption_field_name                protoreflect.Name = "uninterpreted_option"

	MessageOptions_MessageSetWireFormat_field_fullname               protoreflect.FullName = "google.protobuf.MessageOptions.message_set_wire_format"
	MessageOptions_NoStandardDescriptorAccessor_field_fullname       protoreflect.FullName = "google.protobuf.MessageOptions.no_standard_descriptor_accessor"
	MessageOptions_Deprecated_field_fullname                         protoreflect.FullName = "google.protobuf.MessageOptions.deprecated"
	MessageOptions_MapEntry_field_fullname                           protoreflect.FullName = "google.protobuf.MessageOptions.map_entry"
	MessageOptions_DeprecatedLegacyJsonFieldConflicts_field_fullname protoreflect.FullName = "google.protobuf.MessageOptions.deprecated_legacy_json_field_conflicts"
	MessageOptions_Features_field_fullname                           protoreflect.FullName = "google.protobuf.MessageOptions.features"
	MessageOptions_UninterpretedOption_field_fullname                protoreflect.FullName = "google.protobuf.MessageOptions.uninterpreted_option"
)

// Field numbers for google.protobuf.MessageOptions.
const (
	MessageOptions_MessageSetWireFormat_field_number               protoreflect.FieldNumber = 1
	MessageOptions_NoStandardDescriptorAccessor_field_number       protoreflect.FieldNumber = 2
	MessageOptions_Deprecated_field_number                         protoreflect.FieldNumber = 3
	MessageOptions_MapEntry_field_number                           protoreflect.FieldNumber = 7
	MessageOptions_DeprecatedLegacyJsonFieldConflicts_field_number protoreflect.FieldNumber = 11
	MessageOptions_Features_field_number                           protoreflect.FieldNumber = 12
	MessageOptions_UninterpretedOption_field_number                protoreflect.FieldNumber = 999
)

// Names for google.protobuf.FieldOptions.
const (
	FieldOptions_message_name     protoreflect.Name     = "FieldOptions"
	FieldOptions_message_fullname protoreflect.FullName = "google.protobuf.FieldOptions"
)

// Field names for google.protobuf.FieldOptions.
const (
	FieldOptions_Ctype_field_name               protoreflect.Name = "ctype"
	FieldOptions_Packed_field_name              protoreflect.Name = "packed"
	FieldOptions_Jstype_field_name              protoreflect.Name = "jstype"
	FieldOptions_Lazy_field_name                protoreflect.Name = "lazy"
	FieldOptions_UnverifiedLazy_field_name      protoreflect.Name = "unverified_lazy"
	FieldOptions_Deprecated_field_name          protoreflect.Name = "deprecated"
	FieldOptions_Weak_field_name                protoreflect.Name = "weak"
	FieldOptions_DebugRedact_field_name         protoreflect.Name = "debug_redact"
	FieldOptions_Retention_field_name           protoreflect.Name = "retention"
	FieldOptions_Targets_field_name             protoreflect.Name = "targets"
	FieldOptions_EditionDefaults_field_name     protoreflect.Name = "edition_defaults"
	FieldOptions_Features_field_name            protoreflect.Name = "features"
	FieldOptions_FeatureSupport_field_name      protoreflect.Name = "feature_support"
	FieldOptions_UninterpretedOption_field_name protoreflect.Name = "uninterpreted_option"

	FieldOptions_Ctype_field_fullname               protoreflect.FullName = "google.protobuf.FieldOptions.ctype"
	FieldOptions_Packed_field_fullname              protoreflect.FullName = "google.protobuf.FieldOptions.packed"
	FieldOptions_Jstype_field_fullname              protoreflect.FullName = "google.protobuf.FieldOptions.jstype"
	FieldOptions_Lazy_field_fullname                protoreflect.FullName = "google.protobuf.FieldOptions.lazy"
	FieldOptions_UnverifiedLazy_field_fullname      protoreflect.FullName = "google.protobuf.FieldOptions.unverified_lazy"
	FieldOptions_Deprecated_field_fullname          protoreflect.FullName = "google.protobuf.FieldOptions.deprecated"
	FieldOptions_Weak_field_fullname                protoreflect.FullName = "google.protobuf.FieldOptions.weak"
	FieldOptions_DebugRedact_field_fullname         protoreflect.FullName = "google.protobuf.FieldOptions.debug_redact"
	FieldOptions_Retention_field_fullname           protoreflect.FullName = "google.protobuf.FieldOptions.retention"
	FieldOptions_Targets_field_fullname             protoreflect.FullName = "google.protobuf.FieldOptions.targets"
	FieldOptions_EditionDefaults_field_fullname     protoreflect.FullName = "google.protobuf.FieldOptions.edition_defaults"
	FieldOptions_Features_field_fullname            protoreflect.FullName = "google.protobuf.FieldOptions.features"
	FieldOptions_FeatureSupport_field_fullname      protoreflect.FullName = "google.protobuf.FieldOptions.feature_support"
	FieldOptions_UninterpretedOption_field_fullname protoreflect.FullName = "google.protobuf.FieldOptions.uninterpreted_option"
)

// Field numbers for google.protobuf.FieldOptions.
const (
	FieldOptions_Ctype_field_number               protoreflect.FieldNumber = 1
	FieldOptions_Packed_field_number              protoreflect.FieldNumber = 2
	FieldOptions_Jstype_field_number              protoreflect.FieldNumber = 6
	FieldOptions_Lazy_field_number                protoreflect.FieldNumber = 5
	FieldOptions_UnverifiedLazy_field_number      protoreflect.FieldNumber = 15
	FieldOptions_Deprecated_field_number          protoreflect.FieldNumber = 3
	FieldOptions_Weak_field_number                protoreflect.FieldNumber = 10
	FieldOptions_DebugRedact_field_number         protoreflect.FieldNumber = 16
	FieldOptions_Retention_field_number           protoreflect.FieldNumber = 17
	FieldOptions_Targets_field_number             protoreflect.FieldNumber = 19
	FieldOptions_EditionDefaults_field_number     protoreflect.FieldNumber = 20
	FieldOptions_Features_field_number            protoreflect.FieldNumber = 21
	FieldOptions_FeatureSupport_field_number      protoreflect.FieldNumber = 22
	FieldOptions_UninterpretedOption_field_number protoreflect.FieldNumber = 999
)

// Full and short names for google.protobuf.FieldOptions.CType.
const (
	FieldOptions_CType_enum_fullname = "google.protobuf.FieldOptions.CType"
	FieldOptions_CType_enum_name     = "CType"
)

// Enum values for google.protobuf.FieldOptions.CType.
const (
	FieldOptions_STRING_enum_value       = 0
	FieldOptions_CORD_enum_value         = 1
	FieldOptions_STRING_PIECE_enum_value = 2
)

// Full and short names for google.protobuf.FieldOptions.JSType.
const (
	FieldOptions_JSType_enum_fullname = "google.protobuf.FieldOptions.JSType"
	FieldOptions_JSType_enum_name     = "JSType"
)

// Enum values for google.protobuf.FieldOptions.JSType.
const (
	FieldOptions_JS_NORMAL_enum_value = 0
	FieldOptions_JS_STRING_enum_value = 1
	FieldOptions_JS_NUMBER_enum_value = 2
)

// Full and short names for google.protobuf.FieldOptions.OptionRetention.
const (
	FieldOptions_OptionRetention_enum_fullname = "google.protobuf.FieldOptions.OptionRetention"
	FieldOptions_OptionRetention_enum_name     = "OptionRetention"
)

// Enum values for google.protobuf.FieldOptions.OptionRetention.
const (
	FieldOptions_RETENTION_UNKNOWN_enum_value = 0
	FieldOptions_RETENTION_RUNTIME_enum_value = 1
	FieldOptions_RETENTION_SOURCE_enum_value  = 2
)

// Full and short names for google.protobuf.FieldOptions.OptionTargetType.
const (
	FieldOptions_OptionTargetType_enum_fullname = "google.protobuf.FieldOptions.OptionTargetType"
	FieldOptions_OptionTargetType_enum_name     = "OptionTargetType"
)

// Enum values for google.protobuf.FieldOptions.OptionTargetType.
const (
	FieldOptions_TARGET_TYPE_UNKNOWN_enum_value         = 0
	FieldOptions_TARGET_TYPE_FILE_enum_value            = 1
	FieldOptions_TARGET_TYPE_EXTENSION_RANGE_enum_value = 2
	FieldOptions_TARGET_TYPE_MESSAGE_enum_value         = 3
	FieldOptions_TARGET_TYPE_FIELD_enum_value           = 4
	FieldOptions_TARGET_TYPE_ONEOF_enum_value           = 5
	FieldOptions_TARGET_TYPE_ENUM_enum_value            = 6
	FieldOptions_TARGET_TYPE_ENUM_ENTRY_enum_value      = 7
	FieldOptions_TARGET_TYPE_SERVICE_enum_value         = 8
	FieldOptions_TARGET_TYPE_METHOD_enum_value          = 9
)

// Names for google.protobuf.FieldOptions.EditionDefault.
const (
	FieldOptions_EditionDefault_message_name     protoreflect.Name     = "EditionDefault"
	FieldOptions_EditionDefault_message_fullname protoreflect.FullName = "google.protobuf.FieldOptions.EditionDefault"
)

// Field names for google.protobuf.FieldOptions.EditionDefault.
const (
	FieldOptions_EditionDefault_Edition_field_name protoreflect.Name = "edition"
	FieldOptions_EditionDefault_Value_field_name   protoreflect.Name = "value"

	FieldOptions_EditionDefault_Edition_field_fullname protoreflect.FullName = "google.protobuf.FieldOptions.EditionDefault.edition"
	FieldOptions_EditionDefault_Value_field_fullname   protoreflect.FullName = "google.protobuf.FieldOptions.EditionDefault.value"
)

// Field numbers for google.protobuf.FieldOptions.EditionDefault.
const (
	FieldOptions_EditionDefault_Edition_field_number protoreflect.FieldNumber = 3
	FieldOptions_EditionDefault_Value_field_number   protoreflect.FieldNumber = 2
)

// Names for google.protobuf.FieldOptions.FeatureSupport.
const (
	FieldOptions_FeatureSupport_message_name     protoreflect.Name     = "FeatureSupport"
	FieldOptions_FeatureSupport_message_fullname protoreflect.FullName = "google.protobuf.FieldOptions.FeatureSupport"
)

// Field names for google.protobuf.FieldOptions.FeatureSupport.
const (
	FieldOptions_FeatureSupport_EditionIntroduced_field_name  protoreflect.Name = "edition_introduced"
	FieldOptions_FeatureSupport_EditionDeprecated_field_name  protoreflect.Name = "edition_deprecated"
	FieldOptions_FeatureSupport_DeprecationWarning_field_name protoreflect.Name = "deprecation_warning"
	FieldOptions_FeatureSupport_EditionRemoved_field_name     protoreflect.Name = "edition_removed"

	FieldOptions_FeatureSupport_EditionIntroduced_field_fullname  protoreflect.FullName = "google.protobuf.FieldOptions.FeatureSupport.edition_introduced"
	FieldOptions_FeatureSupport_EditionDeprecated_field_fullname  protoreflect.FullName = "google.protobuf.FieldOptions.FeatureSupport.edition_deprecated"
	FieldOptions_FeatureSupport_DeprecationWarning_field_fullname protoreflect.FullName = "google.protobuf.FieldOptions.FeatureSupport.deprecation_warning"
	FieldOptions_FeatureSupport_EditionRemoved_field_fullname     protoreflect.FullName = "google.protobuf.FieldOptions.FeatureSupport.edition_removed"
)

// Field numbers for google.protobuf.FieldOptions.FeatureSupport.
const (
	FieldOptions_FeatureSupport_EditionIntroduced_field_number  protoreflect.FieldNumber = 1
	FieldOptions_FeatureSupport_EditionDeprecated_field_number  protoreflect.FieldNumber = 2
	FieldOptions_FeatureSupport_DeprecationWarning_field_number protoreflect.FieldNumber = 3
	FieldOptions_FeatureSupport_EditionRemoved_field_number     protoreflect.FieldNumber = 4
)

// Names for google.protobuf.OneofOptions.
const (
	OneofOptions_message_name     protoreflect.Name     = "OneofOptions"
	OneofOptions_message_fullname protoreflect.FullName = "google.protobuf.OneofOptions"
)

// Field names for google.protobuf.OneofOptions.
const (
	OneofOptions_Features_field_name            protoreflect.Name = "features"
	OneofOptions_UninterpretedOption_field_name protoreflect.Name = "uninterpreted_option"

	OneofOptions_Features_field_fullname            protoreflect.FullName = "google.protobuf.OneofOptions.features"
	OneofOptions_UninterpretedOption_field_fullname protoreflect.FullName = "google.protobuf.OneofOptions.uninterpreted_option"
)

// Field numbers for google.protobuf.OneofOptions.
const (
	OneofOptions_Features_field_number            protoreflect.FieldNumber = 1
	OneofOptions_UninterpretedOption_field_number protoreflect.FieldNumber = 999
)

// Names for google.protobuf.EnumOptions.
const (
	EnumOptions_message_name     protoreflect.Name     = "EnumOptions"
	EnumOptions_message_fullname protoreflect.FullName = "google.protobuf.EnumOptions"
)

// Field names for google.protobuf.EnumOptions.
const (
	EnumOptions_AllowAlias_field_name                         protoreflect.Name = "allow_alias"
	EnumOptions_Deprecated_field_name                         protoreflect.Name = "deprecated"
	EnumOptions_DeprecatedLegacyJsonFieldConflicts_field_name protoreflect.Name = "deprecated_legacy_json_field_conflicts"
	EnumOptions_Features_field_name                           protoreflect.Name = "features"
	EnumOptions_UninterpretedOption_field_name                protoreflect.Name = "uninterpreted_option"

	EnumOptions_AllowAlias_field_fullname                         protoreflect.FullName = "google.protobuf.EnumOptions.allow_alias"
	EnumOptions_Deprecated_field_fullname                         protoreflect.FullName = "google.protobuf.EnumOptions.deprecated"
	EnumOptions_DeprecatedLegacyJsonFieldConflicts_field_fullname protoreflect.FullName = "google.protobuf.EnumOptions.deprecated_legacy_json_field_conflicts"
	EnumOptions_Features_field_fullname                           protoreflect.FullName = "google.protobuf.EnumOptions.features"
	EnumOptions_UninterpretedOption_field_fullname                protoreflect.FullName = "google.protobuf.EnumOptions.uninterpreted_option"
)

// Field numbers for google.protobuf.EnumOptions.
const (
	EnumOptions_AllowAlias_field_number                         protoreflect.FieldNumber = 2
	EnumOptions_Deprecated_field_number                         protoreflect.FieldNumber = 3
	EnumOptions_DeprecatedLegacyJsonFieldConflicts_field_number protoreflect.FieldNumber = 6
	EnumOptions_Features_field_number                           protoreflect.FieldNumber = 7
	EnumOptions_UninterpretedOption_field_number                protoreflect.FieldNumber = 999
)

// Names for google.protobuf.EnumValueOptions.
const (
	EnumValueOptions_message_name     protoreflect.Name     = "EnumValueOptions"
	EnumValueOptions_message_fullname protoreflect.FullName = "google.protobuf.EnumValueOptions"
)

// Field names for google.protobuf.EnumValueOptions.
const (
	EnumValueOptions_Deprecated_field_name          protoreflect.Name = "deprecated"
	EnumValueOptions_Features_field_name            protoreflect.Name = "features"
	EnumValueOptions_DebugRedact_field_name         protoreflect.Name = "debug_redact"
	EnumValueOptions_FeatureSupport_field_name      protoreflect.Name = "feature_support"
	EnumValueOptions_UninterpretedOption_field_name protoreflect.Name = "uninterpreted_option"

	EnumValueOptions_Deprecated_field_fullname          protoreflect.FullName = "google.protobuf.EnumValueOptions.deprecated"
	EnumValueOptions_Features_field_fullname            protoreflect.FullName = "google.protobuf.EnumValueOptions.features"
	EnumValueOptions_DebugRedact_field_fullname         protoreflect.FullName = "google.protobuf.EnumValueOptions.debug_redact"
	EnumValueOptions_FeatureSupport_field_fullname      protoreflect.FullName = "google.protobuf.EnumValueOptions.feature_support"
	EnumValueOptions_UninterpretedOption_field_fullname protoreflect.FullName = "google.protobuf.EnumValueOptions.uninterpreted_option"
)

// Field numbers for google.protobuf.EnumValueOptions.
const (
	EnumValueOptions_Deprecated_field_number          protoreflect.FieldNumber = 1
	EnumValueOptions_Features_field_number            protoreflect.FieldNumber = 2
	EnumValueOptions_DebugRedact_field_number         protoreflect.FieldNumber = 3
	EnumValueOptions_FeatureSupport_field_number      protoreflect.FieldNumber = 4
	EnumValueOptions_UninterpretedOption_field_number protoreflect.FieldNumber = 999
)

// Names for google.protobuf.ServiceOptions.
const (
	ServiceOptions_message_name     protoreflect.Name     = "ServiceOptions"
	ServiceOptions_message_fullname protoreflect.FullName = "google.protobuf.ServiceOptions"
)

// Field names for google.protobuf.ServiceOptions.
const (
	ServiceOptions_Features_field_name            protoreflect.Name = "features"
	ServiceOptions_Deprecated_field_name          protoreflect.Name = "deprecated"
	ServiceOptions_UninterpretedOption_field_name protoreflect.Name = "uninterpreted_option"

	ServiceOptions_Features_field_fullname            protoreflect.FullName = "google.protobuf.ServiceOptions.features"
	ServiceOptions_Deprecated_field_fullname          protoreflect.FullName = "google.protobuf.ServiceOptions.deprecated"
	ServiceOptions_UninterpretedOption_field_fullname protoreflect.FullName = "google.protobuf.ServiceOptions.uninterpreted_option"
)

// Field numbers for google.protobuf.ServiceOptions.
const (
	ServiceOptions_Features_field_number            protoreflect.FieldNumber = 34
	ServiceOptions_Deprecated_field_number          protoreflect.FieldNumber = 33
	ServiceOptions_UninterpretedOption_field_number protoreflect.FieldNumber = 999
)

// Names for google.protobuf.MethodOptions.
const (
	MethodOptions_message_name     protoreflect.Name     = "MethodOptions"
	MethodOptions_message_fullname protoreflect.FullName = "google.protobuf.MethodOptions"
)

// Field names for google.protobuf.MethodOptions.
const (
	MethodOptions_Deprecated_field_name          protoreflect.Name = "deprecated"
	MethodOptions_IdempotencyLevel_field_name    protoreflect.Name = "idempotency_level"
	MethodOptions_Features_field_name            protoreflect.Name = "features"
	MethodOptions_UninterpretedOption_field_name protoreflect.Name = "uninterpreted_option"

	MethodOptions_Deprecated_field_fullname          protoreflect.FullName = "google.protobuf.MethodOptions.deprecated"
	MethodOptions_IdempotencyLevel_field_fullname    protoreflect.FullName = "google.protobuf.MethodOptions.idempotency_level"
	MethodOptions_Features_field_fullname            protoreflect.FullName = "google.protobuf.MethodOptions.features"
	MethodOptions_UninterpretedOption_field_fullname protoreflect.FullName = "google.protobuf.MethodOptions.uninterpreted_option"
)

// Field numbers for google.protobuf.MethodOptions.
const (
	MethodOptions_Deprecated_field_number          protoreflect.FieldNumber = 33
	MethodOptions_IdempotencyLevel_field_number    protoreflect.FieldNumber = 34
	MethodOptions_Features_field_number            protoreflect.FieldNumber = 35
	MethodOptions_UninterpretedOption_field_number protoreflect.FieldNumber = 999
)

// Full and short names for google.protobuf.MethodOptions.IdempotencyLevel.
const (
	MethodOptions_IdempotencyLevel_enum_fullname = "google.protobuf.MethodOptions.IdempotencyLevel"
	MethodOptions_IdempotencyLevel_enum_name     = "IdempotencyLevel"
)

// Enum values for google.protobuf.MethodOptions.IdempotencyLevel.
const (
	MethodOptions_IDEMPOTENCY_UNKNOWN_enum_value = 0
	MethodOptions_NO_SIDE_EFFECTS_enum_value     = 1
	MethodOptions_IDEMPOTENT_enum_value          = 2
)

// Names for google.protobuf.UninterpretedOption.
const (
	UninterpretedOption_message_name     protoreflect.Name     = "UninterpretedOption"
	UninterpretedOption_message_fullname protoreflect.FullName = "google.protobuf.UninterpretedOption"
)

// Field names for google.protobuf.UninterpretedOption.
const (
	UninterpretedOption_Name_field_name             protoreflect.Name = "name"
	UninterpretedOption_IdentifierValue_field_name  protoreflect.Name = "identifier_value"
	UninterpretedOption_PositiveIntValue_field_name protoreflect.Name = "positive_int_value"
	UninterpretedOption_NegativeIntValue_field_name protoreflect.Name = "negative_int_value"
	UninterpretedOption_DoubleValue_field_name      protoreflect.Name = "double_value"
	UninterpretedOption_StringValue_field_name      protoreflect.Name = "string_value"
	UninterpretedOption_AggregateValue_field_name   protoreflect.Name = "aggregate_value"

	UninterpretedOption_Name_field_fullname             protoreflect.FullName = "google.protobuf.UninterpretedOption.name"
	UninterpretedOption_IdentifierValue_field_fullname  protoreflect.FullName = "google.protobuf.UninterpretedOption.identifier_value"
	UninterpretedOption_PositiveIntValue_field_fullname protoreflect.FullName = "google.protobuf.UninterpretedOption.positive_int_value"
	UninterpretedOption_NegativeIntValue_field_fullname protoreflect.FullName = "google.protobuf.UninterpretedOption.negative_int_value"
	UninterpretedOption_DoubleValue_field_fullname      protoreflect.FullName = "google.protobuf.UninterpretedOption.double_value"
	UninterpretedOption_StringValue_field_fullname      protoreflect.FullName = "google.protobuf.UninterpretedOption.string_value"
	UninterpretedOption_AggregateValue_field_fullname   protoreflect.FullName = "google.protobuf.UninterpretedOption.aggregate_value"
)

// Field numbers for google.protobuf.UninterpretedOption.
const (
	UninterpretedOption_Name_field_number             protoreflect.FieldNumber = 2
	UninterpretedOption_IdentifierValue_field_number  protoreflect.FieldNumber = 3
	UninterpretedOption_PositiveIntValue_field_number protoreflect.FieldNumber = 4
	UninterpretedOption_NegativeIntValue_field_number protoreflect.FieldNumber = 5
	UninterpretedOption_DoubleValue_field_number      protoreflect.FieldNumber = 6
	UninterpretedOption_StringValue_field_number      protoreflect.FieldNumber = 7
	UninterpretedOption_AggregateValue_field_number   protoreflect.FieldNumber = 8
)

// Names for google.protobuf.UninterpretedOption.NamePart.
const (
	UninterpretedOption_NamePart_message_name     protoreflect.Name     = "NamePart"
	UninterpretedOption_NamePart_message_fullname protoreflect.FullName = "google.protobuf.UninterpretedOption.NamePart"
)

// Field names for google.protobuf.UninterpretedOption.NamePart.
const (
	UninterpretedOption_NamePart_NamePart_field_name    protoreflect.Name = "name_part"
	UninterpretedOption_NamePart_IsExtension_field_name protoreflect.Name = "is_extension"

	UninterpretedOption_NamePart_NamePart_field_fullname    protoreflect.FullName = "google.protobuf.UninterpretedOption.NamePart.name_part"
	UninterpretedOption_NamePart_IsExtension_field_fullname protoreflect.FullName = "google.protobuf.UninterpretedOption.NamePart.is_extension"
)

// Field numbers for google.protobuf.UninterpretedOption.NamePart.
const (
	UninterpretedOption_NamePart_NamePart_field_number    protoreflect.FieldNumber = 1
	UninterpretedOption_NamePart_IsExtension_field_number protoreflect.FieldNumber = 2
)

// Names for google.protobuf.FeatureSet.
const (
	FeatureSet_message_name     protoreflect.Name     = "FeatureSet"
	FeatureSet_message_fullname protoreflect.FullName = "google.protobuf.FeatureSet"
)

// Field names for google.protobuf.FeatureSet.
const (
	FeatureSet_FieldPresence_field_name         protoreflect.Name = "field_presence"
	FeatureSet_EnumType_field_name              protoreflect.Name = "enum_type"
	FeatureSet_RepeatedFieldEncoding_field_name protoreflect.Name = "repeated_field_encoding"
	FeatureSet_Utf8Validation_field_name        protoreflect.Name = "utf8_validation"
	FeatureSet_MessageEncoding_field_name       protoreflect.Name = "message_encoding"
	FeatureSet_JsonFormat_field_name            protoreflect.Name = "json_format"

	FeatureSet_FieldPresence_field_fullname         protoreflect.FullName = "google.protobuf.FeatureSet.field_presence"
	FeatureSet_EnumType_field_fullname              protoreflect.FullName = "google.protobuf.FeatureSet.enum_type"
	FeatureSet_RepeatedFieldEncoding_field_fullname protoreflect.FullName = "google.protobuf.FeatureSet.repeated_field_encoding"
	FeatureSet_Utf8Validation_field_fullname        protoreflect.FullName = "google.protobuf.FeatureSet.utf8_validation"
	FeatureSet_MessageEncoding_field_fullname       protoreflect.FullName = "google.protobuf.FeatureSet.message_encoding"
	FeatureSet_JsonFormat_field_fullname            protoreflect.FullName = "google.protobuf.FeatureSet.json_format"
)

// Field numbers for google.protobuf.FeatureSet.
const (
	FeatureSet_FieldPresence_field_number         protoreflect.FieldNumber = 1
	FeatureSet_EnumType_field_number              protoreflect.FieldNumber = 2
	FeatureSet_RepeatedFieldEncoding_field_number protoreflect.FieldNumber = 3
	FeatureSet_Utf8Validation_field_number        protoreflect.FieldNumber = 4
	FeatureSet_MessageEncoding_field_number       protoreflect.FieldNumber = 5
	FeatureSet_JsonFormat_field_number            protoreflect.FieldNumber = 6
)

// Full and short names for google.protobuf.FeatureSet.FieldPresence.
const (
	FeatureSet_FieldPresence_enum_fullname = "google.protobuf.FeatureSet.FieldPresence"
	FeatureSet_FieldPresence_enum_name     = "FieldPresence"
)

// Enum values for google.protobuf.FeatureSet.FieldPresence.
const (
	FeatureSet_FIELD_PRESENCE_UNKNOWN_enum_value = 0
	FeatureSet_EXPLICIT_enum_value               = 1
	FeatureSet_IMPLICIT_enum_value               = 2
	FeatureSet_LEGACY_REQUIRED_enum_value        = 3
)

// Full and short names for google.protobuf.FeatureSet.EnumType.
const (
	FeatureSet_EnumType_enum_fullname = "google.protobuf.FeatureSet.EnumType"
	FeatureSet_EnumType_enum_name     = "EnumType"
)

// Enum values for google.protobuf.FeatureSet.EnumType.
const (
	FeatureSet_ENUM_TYPE_UNKNOWN_enum_value = 0
	FeatureSet_OPEN_enum_value              = 1
	FeatureSet_CLOSED_enum_value            = 2
)

// Full and short names for google.protobuf.FeatureSet.RepeatedFieldEncoding.
const (
	FeatureSet_RepeatedFieldEncoding_enum_fullname = "google.protobuf.FeatureSet.RepeatedFieldEncoding"
	FeatureSet_RepeatedFieldEncoding_enum_name     = "RepeatedFieldEncoding"
)

// Enum values for google.protobuf.FeatureSet.RepeatedFieldEncoding.
const (
	FeatureSet_REPEATED_FIELD_ENCODING_UNKNOWN_enum_value = 0
	FeatureSet_PACKED_enum_value                          = 1
	FeatureSet_EXPANDED_enum_value                        = 2
)

// Full and short names for google.protobuf.FeatureSet.Utf8Validation.
const (
	FeatureSet_Utf8Validation_enum_fullname = "google.protobuf.FeatureSet.Utf8Validation"
	FeatureSet_Utf8Validation_enum_name     = "Utf8Validation"
)

// Enum values for google.protobuf.FeatureSet.Utf8Validation.
const (
	FeatureSet_UTF8_VALIDATION_UNKNOWN_enum_value = 0
	FeatureSet_VERIFY_enum_value                  = 2
	FeatureSet_NONE_enum_value                    = 3
)

// Full and short names for google.protobuf.FeatureSet.MessageEncoding.
const (
	FeatureSet_MessageEncoding_enum_fullname = "google.protobuf.FeatureSet.MessageEncoding"
	FeatureSet_MessageEncoding_enum_name     = "MessageEncoding"
)

// Enum values for google.protobuf.FeatureSet.MessageEncoding.
const (
	FeatureSet_MESSAGE_ENCODING_UNKNOWN_enum_value = 0
	FeatureSet_LENGTH_PREFIXED_enum_value          = 1
	FeatureSet_DELIMITED_enum_value                = 2
)

// Full and short names for google.protobuf.FeatureSet.JsonFormat.
const (
	FeatureSet_JsonFormat_enum_fullname = "google.protobuf.FeatureSet.JsonFormat"
	FeatureSet_JsonFormat_enum_name     = "JsonFormat"
)

// Enum values for google.protobuf.FeatureSet.JsonFormat.
const (
	FeatureSet_JSON_FORMAT_UNKNOWN_enum_value = 0
	FeatureSet_ALLOW_enum_value               = 1
	FeatureSet_LEGACY_BEST_EFFORT_enum_value  = 2
)

// Names for google.protobuf.FeatureSetDefaults.
const (
	FeatureSetDefaults_message_name     protoreflect.Name     = "FeatureSetDefaults"
	FeatureSetDefaults_message_fullname protoreflect.FullName = "google.protobuf.FeatureSetDefaults"
)

// Field names for google.protobuf.FeatureSetDefaults.
const (
	FeatureSetDefaults_Defaults_field_name       protoreflect.Name = "defaults"
	FeatureSetDefaults_MinimumEdition_field_name protoreflect.Name = "minimum_edition"
	FeatureSetDefaults_MaximumEdition_field_name protoreflect.Name = "maximum_edition"

	FeatureSetDefaults_Defaults_field_fullname       protoreflect.FullName = "google.protobuf.FeatureSetDefaults.defaults"
	FeatureSetDefaults_MinimumEdition_field_fullname protoreflect.FullName = "google.protobuf.FeatureSetDefaults.minimum_edition"
	FeatureSetDefaults_MaximumEdition_field_fullname protoreflect.FullName = "google.protobuf.FeatureSetDefaults.maximum_edition"
)

// Field numbers for google.protobuf.FeatureSetDefaults.
const (
	FeatureSetDefaults_Defaults_field_number       protoreflect.FieldNumber = 1
	FeatureSetDefaults_MinimumEdition_field_number protoreflect.FieldNumber = 4
	FeatureSetDefaults_MaximumEdition_field_number protoreflect.FieldNumber = 5
)

// Names for google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.
const (
	FeatureSetDefaults_FeatureSetEditionDefault_message_name     protoreflect.Name     = "FeatureSetEditionDefault"
	FeatureSetDefaults_FeatureSetEditionDefault_message_fullname protoreflect.FullName = "google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault"
)

// Field names for google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.
const (
	FeatureSetDefaults_FeatureSetEditionDefault_Edition_field_name             protoreflect.Name = "edition"
	FeatureSetDefaults_FeatureSetEditionDefault_OverridableFeatures_field_name protoreflect.Name = "overridable_features"
	FeatureSetDefaults_FeatureSetEditionDefault_FixedFeatures_field_name       protoreflect.Name = "fixed_features"

	FeatureSetDefaults_FeatureSetEditionDefault_Edition_field_fullname             protoreflect.FullName = "google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.edition"
	FeatureSetDefaults_FeatureSetEditionDefault_OverridableFeatures_field_fullname protoreflect.FullName = "google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.overridable_features"
	FeatureSetDefaults_FeatureSetEditionDefault_FixedFeatures_field_fullname       protoreflect.FullName = "google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.fixed_features"
)

// Field numbers for google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.
const (
	FeatureSetDefaults_FeatureSetEditionDefault_Edition_field_number             protoreflect.FieldNumber = 3
	FeatureSetDefaults_FeatureSetEditionDefault_OverridableFeatures_field_number protoreflect.FieldNumber = 4
	FeatureSetDefaults_FeatureSetEditionDefault_FixedFeatures_field_number       protoreflect.FieldNumber = 5
)

// Names for google.protobuf.SourceCodeInfo.
const (
	SourceCodeInfo_message_name     protoreflect.Name     = "SourceCodeInfo"
	SourceCodeInfo_message_fullname protoreflect.FullName = "google.protobuf.SourceCodeInfo"
)

// Field names for google.protobuf.SourceCodeInfo.
const (
	SourceCodeInfo_Location_field_name protoreflect.Name = "location"

	SourceCodeInfo_Location_field_fullname protoreflect.FullName = "google.protobuf.SourceCodeInfo.location"
)

// Field numbers for google.protobuf.SourceCodeInfo.
const (
	SourceCodeInfo_Location_field_number protoreflect.FieldNumber = 1
)

// Names for google.protobuf.SourceCodeInfo.Location.
const (
	SourceCodeInfo_Location_message_name     protoreflect.Name     = "Location"
	SourceCodeInfo_Location_message_fullname protoreflect.FullName = "google.protobuf.SourceCodeInfo.Location"
)

// Field names for google.protobuf.SourceCodeInfo.Location.
const (
	SourceCodeInfo_Location_Path_field_name                    protoreflect.Name = "path"
	SourceCodeInfo_Location_Span_field_name                    protoreflect.Name = "span"
	SourceCodeInfo_Location_LeadingComments_field_name         protoreflect.Name = "leading_comments"
	SourceCodeInfo_Location_TrailingComments_field_name        protoreflect.Name = "trailing_comments"
	SourceCodeInfo_Location_LeadingDetachedComments_field_name protoreflect.Name = "leading_detached_comments"

	SourceCodeInfo_Location_Path_field_fullname                    protoreflect.FullName = "google.protobuf.SourceCodeInfo.Location.path"
	SourceCodeInfo_Location_Span_field_fullname                    protoreflect.FullName = "google.protobuf.SourceCodeInfo.Location.span"
	SourceCodeInfo_Location_LeadingComments_field_fullname         protoreflect.FullName = "google.protobuf.SourceCodeInfo.Location.leading_comments"
	SourceCodeInfo_Location_TrailingComments_field_fullname        protoreflect.FullName = "google.protobuf.SourceCodeInfo.Location.trailing_comments"
	SourceCodeInfo_Location_LeadingDetachedComments_field_fullname protoreflect.FullName = "google.protobuf.SourceCodeInfo.Location.leading_detached_comments"
)

// Field numbers for google.protobuf.SourceCodeInfo.Location.
const (
	SourceCodeInfo_Location_Path_field_number                    protoreflect.FieldNumber = 1
	SourceCodeInfo_Location_Span_field_number                    protoreflect.FieldNumber = 2
	SourceCodeInfo_Location_LeadingComments_field_number         protoreflect.FieldNumber = 3
	SourceCodeInfo_Location_TrailingComments_field_number        protoreflect.FieldNumber = 4
	SourceCodeInfo_Location_LeadingDetachedComments_field_number protoreflect.FieldNumber = 6
)

// Names for google.protobuf.GeneratedCodeInfo.
const (
	GeneratedCodeInfo_message_name     protoreflect.Name     = "GeneratedCodeInfo"
	GeneratedCodeInfo_message_fullname protoreflect.FullName = "google.protobuf.GeneratedCodeInfo"
)

// Field names for google.protobuf.GeneratedCodeInfo.
const (
	GeneratedCodeInfo_Annotation_field_name protoreflect.Name = "annotation"

	GeneratedCodeInfo_Annotation_field_fullname protoreflect.FullName = "google.protobuf.GeneratedCodeInfo.annotation"
)

// Field numbers for google.protobuf.GeneratedCodeInfo.
const (
	GeneratedCodeInfo_Annotation_field_number protoreflect.FieldNumber = 1
)

// Names for google.protobuf.GeneratedCodeInfo.Annotation.
const (
	GeneratedCodeInfo_Annotation_message_name     protoreflect.Name     = "Annotation"
	GeneratedCodeInfo_Annotation_message_fullname protoreflect.FullName = "google.protobuf.GeneratedCodeInfo.Annotation"
)

// Field names for google.protobuf.GeneratedCodeInfo.Annotation.
const (
	GeneratedCodeInfo_Annotation_Path_field_name       protoreflect.Name = "path"
	GeneratedCodeInfo_Annotation_SourceFile_field_name protoreflect.Name = "source_file"
	GeneratedCodeInfo_Annotation_Begin_field_name      protoreflect.Name = "begin"
	GeneratedCodeInfo_Annotation_End_field_name        protoreflect.Name = "end"
	GeneratedCodeInfo_Annotation_Semantic_field_name   protoreflect.Name = "semantic"

	GeneratedCodeInfo_Annotation_Path_field_fullname       protoreflect.FullName = "google.protobuf.GeneratedCodeInfo.Annotation.path"
	GeneratedCodeInfo_Annotation_SourceFile_field_fullname protoreflect.FullName = "google.protobuf.GeneratedCodeInfo.Annotation.source_file"
	GeneratedCodeInfo_Annotation_Begin_field_fullname      protoreflect.FullName = "google.protobuf.GeneratedCodeInfo.Annotation.begin"
	GeneratedCodeInfo_Annotation_End_field_fullname        protoreflect.FullName = "google.protobuf.GeneratedCodeInfo.Annotation.end"
	GeneratedCodeInfo_Annotation_Semantic_field_fullname   protoreflect.FullName = "google.protobuf.GeneratedCodeInfo.Annotation.semantic"
)

// Field numbers for google.protobuf.GeneratedCodeInfo.Annotation.
const (
	GeneratedCodeInfo_Annotation_Path_field_number       protoreflect.FieldNumber = 1
	GeneratedCodeInfo_Annotation_SourceFile_field_number protoreflect.FieldNumber = 2
	GeneratedCodeInfo_Annotation_Begin_field_number      protoreflect.FieldNumber = 3
	GeneratedCodeInfo_Annotation_End_field_number        protoreflect.FieldNumber = 4
	GeneratedCodeInfo_Annotation_Semantic_field_number   protoreflect.FieldNumber = 5
)

// Full and short names for google.protobuf.GeneratedCodeInfo.Annotation.Semantic.
const (
	GeneratedCodeInfo_Annotation_Semantic_enum_fullname = "google.protobuf.GeneratedCodeInfo.Annotation.Semantic"
	GeneratedCodeInfo_Annotation_Semantic_enum_name     = "Semantic"
)

// Enum values for google.protobuf.GeneratedCodeInfo.Annotation.Semantic.
const (
	GeneratedCodeInfo_Annotation_NONE_enum_value  = 0
	GeneratedCodeInfo_Annotation_SET_enum_value   = 1
	GeneratedCodeInfo_Annotation_ALIAS_enum_value = 2
)
