/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.flowcontrol.v1beta1;

import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/api/flowcontrol/v1beta1";

// ExemptPriorityLevelConfiguration describes the configurable aspects
// of the handling of exempt requests.
// In the mandatory exempt configuration object the values in the fields
// here can be modified by authorized users, unlike the rest of the `spec`.
message ExemptPriorityLevelConfiguration {
  // `nominalConcurrencyShares` (NCS) contributes to the computation of the
  // NominalConcurrencyLimit (NominalCL) of this level.
  // This is the number of execution seats nominally reserved for this priority level.
  // This DOES NOT limit the dispatching from this priority level
  // but affects the other priority levels through the borrowing mechanism.
  // The server's concurrency limit (ServerCL) is divided among all the
  // priority levels in proportion to their NCS values:
  //
  // NominalCL(i)  = ceil( ServerCL * NCS(i) / sum_ncs )
  // sum_ncs = sum[priority level k] NCS(k)
  //
  // Bigger numbers mean a larger nominal concurrency limit,
  // at the expense of every other priority level.
  // This field has a default value of zero.
  // +optional
  optional int32 nominalConcurrencyShares = 1;

  // `lendablePercent` prescribes the fraction of the level's NominalCL that
  // can be borrowed by other priority levels.  This value of this
  // field must be between 0 and 100, inclusive, and it defaults to 0.
  // The number of seats that other levels can borrow from this level, known
  // as this level's LendableConcurrencyLimit (LendableCL), is defined as follows.
  //
  // LendableCL(i) = round( NominalCL(i) * lendablePercent(i)/100.0 )
  //
  // +optional
  optional int32 lendablePercent = 2;
}

// FlowDistinguisherMethod specifies the method of a flow distinguisher.
message FlowDistinguisherMethod {
  // `type` is the type of flow distinguisher method
  // The supported types are "ByUser" and "ByNamespace".
  // Required.
  optional string type = 1;
}

// FlowSchema defines the schema of a group of flows. Note that a flow is made up of a set of inbound API requests with
// similar attributes and is identified by a pair of strings: the name of the FlowSchema and a "flow distinguisher".
message FlowSchema {
  // `metadata` is the standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // `spec` is the specification of the desired behavior of a FlowSchema.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional FlowSchemaSpec spec = 2;

  // `status` is the current status of a FlowSchema.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional FlowSchemaStatus status = 3;
}

// FlowSchemaCondition describes conditions for a FlowSchema.
message FlowSchemaCondition {
  // `type` is the type of the condition.
  // Required.
  optional string type = 1;

  // `status` is the status of the condition.
  // Can be True, False, Unknown.
  // Required.
  optional string status = 2;

  // `lastTransitionTime` is the last time the condition transitioned from one status to another.
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 3;

  // `reason` is a unique, one-word, CamelCase reason for the condition's last transition.
  optional string reason = 4;

  // `message` is a human-readable message indicating details about last transition.
  optional string message = 5;
}

// FlowSchemaList is a list of FlowSchema objects.
message FlowSchemaList {
  // `metadata` is the standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // `items` is a list of FlowSchemas.
  repeated FlowSchema items = 2;
}

// FlowSchemaSpec describes how the FlowSchema's specification looks like.
message FlowSchemaSpec {
  // `priorityLevelConfiguration` should reference a PriorityLevelConfiguration in the cluster. If the reference cannot
  // be resolved, the FlowSchema will be ignored and marked as invalid in its status.
  // Required.
  optional PriorityLevelConfigurationReference priorityLevelConfiguration = 1;

  // `matchingPrecedence` is used to choose among the FlowSchemas that match a given request. The chosen
  // FlowSchema is among those with the numerically lowest (which we take to be logically highest)
  // MatchingPrecedence.  Each MatchingPrecedence value must be ranged in [1,10000].
  // Note that if the precedence is not specified, it will be set to 1000 as default.
  // +optional
  optional int32 matchingPrecedence = 2;

  // `distinguisherMethod` defines how to compute the flow distinguisher for requests that match this schema.
  // `nil` specifies that the distinguisher is disabled and thus will always be the empty string.
  // +optional
  optional FlowDistinguisherMethod distinguisherMethod = 3;

  // `rules` describes which requests will match this flow schema. This FlowSchema matches a request if and only if
  // at least one member of rules matches the request.
  // if it is an empty slice, there will be no requests matching the FlowSchema.
  // +listType=atomic
  // +optional
  repeated PolicyRulesWithSubjects rules = 4;
}

// FlowSchemaStatus represents the current state of a FlowSchema.
message FlowSchemaStatus {
  // `conditions` is a list of the current states of FlowSchema.
  // +listType=map
  // +listMapKey=type
  // +optional
  repeated FlowSchemaCondition conditions = 1;
}

// GroupSubject holds detailed information for group-kind subject.
message GroupSubject {
  // name is the user group that matches, or "*" to match all user groups.
  // See https://github.com/kubernetes/apiserver/blob/master/pkg/authentication/user/user.go for some
  // well-known group names.
  // Required.
  optional string name = 1;
}

// LimitResponse defines how to handle requests that can not be executed right now.
// +union
message LimitResponse {
  // `type` is "Queue" or "Reject".
  // "Queue" means that requests that can not be executed upon arrival
  // are held in a queue until they can be executed or a queuing limit
  // is reached.
  // "Reject" means that requests that can not be executed upon arrival
  // are rejected.
  // Required.
  // +unionDiscriminator
  optional string type = 1;

  // `queuing` holds the configuration parameters for queuing.
  // This field may be non-empty only if `type` is `"Queue"`.
  // +optional
  optional QueuingConfiguration queuing = 2;
}

// LimitedPriorityLevelConfiguration specifies how to handle requests that are subject to limits.
// It addresses two issues:
//   - How are requests for this priority level limited?
//   - What should be done with requests that exceed the limit?
message LimitedPriorityLevelConfiguration {
  // `assuredConcurrencyShares` (ACS) configures the execution
  // limit, which is a limit on the number of requests of this
  // priority level that may be executing at a given time.  ACS must
  // be a positive number. The server's concurrency limit (SCL) is
  // divided among the concurrency-controlled priority levels in
  // proportion to their assured concurrency shares. This produces
  // the assured concurrency value (ACV) --- the number of requests
  // that may be executing at a time --- for each such priority
  // level:
  //
  //             ACV(l) = ceil( SCL * ACS(l) / ( sum[priority levels k] ACS(k) ) )
  //
  // bigger numbers of ACS mean more reserved concurrent requests (at the
  // expense of every other PL).
  // This field has a default value of 30.
  // +optional
  optional int32 assuredConcurrencyShares = 1;

  // `limitResponse` indicates what to do with requests that can not be executed right now
  optional LimitResponse limitResponse = 2;

  // `lendablePercent` prescribes the fraction of the level's NominalCL that
  // can be borrowed by other priority levels. The value of this
  // field must be between 0 and 100, inclusive, and it defaults to 0.
  // The number of seats that other levels can borrow from this level, known
  // as this level's LendableConcurrencyLimit (LendableCL), is defined as follows.
  //
  // LendableCL(i) = round( NominalCL(i) * lendablePercent(i)/100.0 )
  //
  // +optional
  optional int32 lendablePercent = 3;

  // `borrowingLimitPercent`, if present, configures a limit on how many
  // seats this priority level can borrow from other priority levels.
  // The limit is known as this level's BorrowingConcurrencyLimit
  // (BorrowingCL) and is a limit on the total number of seats that this
  // level may borrow at any one time.
  // This field holds the ratio of that limit to the level's nominal
  // concurrency limit. When this field is non-nil, it must hold a
  // non-negative integer and the limit is calculated as follows.
  //
  // BorrowingCL(i) = round( NominalCL(i) * borrowingLimitPercent(i)/100.0 )
  //
  // The value of this field can be more than 100, implying that this
  // priority level can borrow a number of seats that is greater than
  // its own nominal concurrency limit (NominalCL).
  // When this field is left `nil`, the limit is effectively infinite.
  // +optional
  optional int32 borrowingLimitPercent = 4;
}

// NonResourcePolicyRule is a predicate that matches non-resource requests according to their verb and the
// target non-resource URL. A NonResourcePolicyRule matches a request if and only if both (a) at least one member
// of verbs matches the request and (b) at least one member of nonResourceURLs matches the request.
message NonResourcePolicyRule {
  // `verbs` is a list of matching verbs and may not be empty.
  // "*" matches all verbs. If it is present, it must be the only entry.
  // +listType=set
  // Required.
  repeated string verbs = 1;

  // `nonResourceURLs` is a set of url prefixes that a user should have access to and may not be empty.
  // For example:
  //   - "/healthz" is legal
  //   - "/hea*" is illegal
  //   - "/hea" is legal but matches nothing
  //   - "/hea/*" also matches nothing
  //   - "/healthz/*" matches all per-component health checks.
  // "*" matches all non-resource urls. if it is present, it must be the only entry.
  // +listType=set
  // Required.
  repeated string nonResourceURLs = 6;
}

// PolicyRulesWithSubjects prescribes a test that applies to a request to an apiserver. The test considers the subject
// making the request, the verb being requested, and the resource to be acted upon. This PolicyRulesWithSubjects matches
// a request if and only if both (a) at least one member of subjects matches the request and (b) at least one member
// of resourceRules or nonResourceRules matches the request.
message PolicyRulesWithSubjects {
  // subjects is the list of normal user, serviceaccount, or group that this rule cares about.
  // There must be at least one member in this slice.
  // A slice that includes both the system:authenticated and system:unauthenticated user groups matches every request.
  // +listType=atomic
  // Required.
  repeated Subject subjects = 1;

  // `resourceRules` is a slice of ResourcePolicyRules that identify matching requests according to their verb and the
  // target resource.
  // At least one of `resourceRules` and `nonResourceRules` has to be non-empty.
  // +listType=atomic
  // +optional
  repeated ResourcePolicyRule resourceRules = 2;

  // `nonResourceRules` is a list of NonResourcePolicyRules that identify matching requests according to their verb
  // and the target non-resource URL.
  // +listType=atomic
  // +optional
  repeated NonResourcePolicyRule nonResourceRules = 3;
}

// PriorityLevelConfiguration represents the configuration of a priority level.
message PriorityLevelConfiguration {
  // `metadata` is the standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // `spec` is the specification of the desired behavior of a "request-priority".
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional PriorityLevelConfigurationSpec spec = 2;

  // `status` is the current status of a "request-priority".
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional PriorityLevelConfigurationStatus status = 3;
}

// PriorityLevelConfigurationCondition defines the condition of priority level.
message PriorityLevelConfigurationCondition {
  // `type` is the type of the condition.
  // Required.
  optional string type = 1;

  // `status` is the status of the condition.
  // Can be True, False, Unknown.
  // Required.
  optional string status = 2;

  // `lastTransitionTime` is the last time the condition transitioned from one status to another.
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 3;

  // `reason` is a unique, one-word, CamelCase reason for the condition's last transition.
  optional string reason = 4;

  // `message` is a human-readable message indicating details about last transition.
  optional string message = 5;
}

// PriorityLevelConfigurationList is a list of PriorityLevelConfiguration objects.
message PriorityLevelConfigurationList {
  // `metadata` is the standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // `items` is a list of request-priorities.
  repeated PriorityLevelConfiguration items = 2;
}

// PriorityLevelConfigurationReference contains information that points to the "request-priority" being used.
message PriorityLevelConfigurationReference {
  // `name` is the name of the priority level configuration being referenced
  // Required.
  optional string name = 1;
}

// PriorityLevelConfigurationSpec specifies the configuration of a priority level.
// +union
message PriorityLevelConfigurationSpec {
  // `type` indicates whether this priority level is subject to
  // limitation on request execution.  A value of `"Exempt"` means
  // that requests of this priority level are not subject to a limit
  // (and thus are never queued) and do not detract from the
  // capacity made available to other priority levels.  A value of
  // `"Limited"` means that (a) requests of this priority level
  // _are_ subject to limits and (b) some of the server's limited
  // capacity is made available exclusively to this priority level.
  // Required.
  // +unionDiscriminator
  optional string type = 1;

  // `limited` specifies how requests are handled for a Limited priority level.
  // This field must be non-empty if and only if `type` is `"Limited"`.
  // +optional
  optional LimitedPriorityLevelConfiguration limited = 2;

  // `exempt` specifies how requests are handled for an exempt priority level.
  // This field MUST be empty if `type` is `"Limited"`.
  // This field MAY be non-empty if `type` is `"Exempt"`.
  // If empty and `type` is `"Exempt"` then the default values
  // for `ExemptPriorityLevelConfiguration` apply.
  // +optional
  optional ExemptPriorityLevelConfiguration exempt = 3;
}

// PriorityLevelConfigurationStatus represents the current state of a "request-priority".
message PriorityLevelConfigurationStatus {
  // `conditions` is the current state of "request-priority".
  // +listType=map
  // +listMapKey=type
  // +optional
  repeated PriorityLevelConfigurationCondition conditions = 1;
}

// QueuingConfiguration holds the configuration parameters for queuing
message QueuingConfiguration {
  // `queues` is the number of queues for this priority level. The
  // queues exist independently at each apiserver. The value must be
  // positive.  Setting it to 1 effectively precludes
  // shufflesharding and thus makes the distinguisher method of
  // associated flow schemas irrelevant.  This field has a default
  // value of 64.
  // +optional
  optional int32 queues = 1;

  // `handSize` is a small positive number that configures the
  // shuffle sharding of requests into queues.  When enqueuing a request
  // at this priority level the request's flow identifier (a string
  // pair) is hashed and the hash value is used to shuffle the list
  // of queues and deal a hand of the size specified here.  The
  // request is put into one of the shortest queues in that hand.
  // `handSize` must be no larger than `queues`, and should be
  // significantly smaller (so that a few heavy flows do not
  // saturate most of the queues).  See the user-facing
  // documentation for more extensive guidance on setting this
  // field.  This field has a default value of 8.
  // +optional
  optional int32 handSize = 2;

  // `queueLengthLimit` is the maximum number of requests allowed to
  // be waiting in a given queue of this priority level at a time;
  // excess requests are rejected.  This value must be positive.  If
  // not specified, it will be defaulted to 50.
  // +optional
  optional int32 queueLengthLimit = 3;
}

// ResourcePolicyRule is a predicate that matches some resource
// requests, testing the request's verb and the target resource. A
// ResourcePolicyRule matches a resource request if and only if: (a)
// at least one member of verbs matches the request, (b) at least one
// member of apiGroups matches the request, (c) at least one member of
// resources matches the request, and (d) either (d1) the request does
// not specify a namespace (i.e., `Namespace==""`) and clusterScope is
// true or (d2) the request specifies a namespace and least one member
// of namespaces matches the request's namespace.
message ResourcePolicyRule {
  // `verbs` is a list of matching verbs and may not be empty.
  // "*" matches all verbs and, if present, must be the only entry.
  // +listType=set
  // Required.
  repeated string verbs = 1;

  // `apiGroups` is a list of matching API groups and may not be empty.
  // "*" matches all API groups and, if present, must be the only entry.
  // +listType=set
  // Required.
  repeated string apiGroups = 2;

  // `resources` is a list of matching resources (i.e., lowercase
  // and plural) with, if desired, subresource.  For example, [
  // "services", "nodes/status" ].  This list may not be empty.
  // "*" matches all resources and, if present, must be the only entry.
  // Required.
  // +listType=set
  repeated string resources = 3;

  // `clusterScope` indicates whether to match requests that do not
  // specify a namespace (which happens either because the resource
  // is not namespaced or the request targets all namespaces).
  // If this field is omitted or false then the `namespaces` field
  // must contain a non-empty list.
  // +optional
  optional bool clusterScope = 4;

  // `namespaces` is a list of target namespaces that restricts
  // matches.  A request that specifies a target namespace matches
  // only if either (a) this list contains that target namespace or
  // (b) this list contains "*".  Note that "*" matches any
  // specified namespace but does not match a request that _does
  // not specify_ a namespace (see the `clusterScope` field for
  // that).
  // This list may be empty, but only if `clusterScope` is true.
  // +optional
  // +listType=set
  repeated string namespaces = 5;
}

// ServiceAccountSubject holds detailed information for service-account-kind subject.
message ServiceAccountSubject {
  // `namespace` is the namespace of matching ServiceAccount objects.
  // Required.
  optional string namespace = 1;

  // `name` is the name of matching ServiceAccount objects, or "*" to match regardless of name.
  // Required.
  optional string name = 2;
}

// Subject matches the originator of a request, as identified by the request authentication system. There are three
// ways of matching an originator; by user, group, or service account.
// +union
message Subject {
  // `kind` indicates which one of the other fields is non-empty.
  // Required
  // +unionDiscriminator
  optional string kind = 1;

  // `user` matches based on username.
  // +optional
  optional UserSubject user = 2;

  // `group` matches based on user group name.
  // +optional
  optional GroupSubject group = 3;

  // `serviceAccount` matches ServiceAccounts.
  // +optional
  optional ServiceAccountSubject serviceAccount = 4;
}

// UserSubject holds detailed information for user-kind subject.
message UserSubject {
  // `name` is the username that matches, or "*" to match all usernames.
  // Required.
  optional string name = 1;
}

