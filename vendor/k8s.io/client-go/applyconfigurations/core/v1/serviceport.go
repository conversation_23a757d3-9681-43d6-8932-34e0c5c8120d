/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

import (
	v1 "k8s.io/api/core/v1"
	intstr "k8s.io/apimachinery/pkg/util/intstr"
)

// ServicePortApplyConfiguration represents an declarative configuration of the ServicePort type for use
// with apply.
type ServicePortApplyConfiguration struct {
	Name        *string             `json:"name,omitempty"`
	Protocol    *v1.Protocol        `json:"protocol,omitempty"`
	AppProtocol *string             `json:"appProtocol,omitempty"`
	Port        *int32              `json:"port,omitempty"`
	TargetPort  *intstr.IntOrString `json:"targetPort,omitempty"`
	NodePort    *int32              `json:"nodePort,omitempty"`
}

// ServicePortApplyConfiguration constructs an declarative configuration of the ServicePort type for use with
// apply.
func ServicePort() *ServicePortApplyConfiguration {
	return &ServicePortApplyConfiguration{}
}

// WithName sets the Name field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Name field is set to the value of the last call.
func (b *ServicePortApplyConfiguration) WithName(value string) *ServicePortApplyConfiguration {
	b.Name = &value
	return b
}

// WithProtocol sets the Protocol field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Protocol field is set to the value of the last call.
func (b *ServicePortApplyConfiguration) WithProtocol(value v1.Protocol) *ServicePortApplyConfiguration {
	b.Protocol = &value
	return b
}

// WithAppProtocol sets the AppProtocol field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the AppProtocol field is set to the value of the last call.
func (b *ServicePortApplyConfiguration) WithAppProtocol(value string) *ServicePortApplyConfiguration {
	b.AppProtocol = &value
	return b
}

// WithPort sets the Port field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Port field is set to the value of the last call.
func (b *ServicePortApplyConfiguration) WithPort(value int32) *ServicePortApplyConfiguration {
	b.Port = &value
	return b
}

// WithTargetPort sets the TargetPort field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the TargetPort field is set to the value of the last call.
func (b *ServicePortApplyConfiguration) WithTargetPort(value intstr.IntOrString) *ServicePortApplyConfiguration {
	b.TargetPort = &value
	return b
}

// WithNodePort sets the NodePort field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the NodePort field is set to the value of the last call.
func (b *ServicePortApplyConfiguration) WithNodePort(value int32) *ServicePortApplyConfiguration {
	b.NodePort = &value
	return b
}
