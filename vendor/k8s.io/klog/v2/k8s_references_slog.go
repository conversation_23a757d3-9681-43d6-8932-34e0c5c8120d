//go:build go1.21
// +build go1.21

/*
Copyright 2021 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package klog

import (
	"log/slog"
)

func (ref ObjectRef) LogValue() slog.Value {
	if ref.Namespace != "" {
		return slog.GroupValue(slog.String("name", ref.Name), slog.String("namespace", ref.Namespace))
	}
	return slog.GroupValue(slog.String("name", ref.Name))
}

var _ slog.LogValuer = ObjectRef{}

func (ks kobjSlice) LogValue() slog.Value {
	return slog.AnyValue(ks.MarshalLog())
}

var _ slog.LogValuer = kobjSlice{}
